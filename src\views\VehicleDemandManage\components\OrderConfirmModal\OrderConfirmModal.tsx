import React, {
  forwardRef,
  useCallback,
  useImperativeHandle,
  useMemo,
} from 'react';
import { Form, DatePicker, Divider } from 'antd';
import {
  ExclamationCircleOutlined,
  CheckCircleFilled,
} from '@ant-design/icons';
import dayjs from 'dayjs';
import './OrderConfirmModal.scss';

interface OrderItem {
  requirementId: number;
  vehicleModelType: string;
  vehicleModelName: string;
  count: number;
  stationName: string;
  contact: string;
  contactPhone: string;
  stationNumber: string;
  address: string;
  expectedDeliveryMonth?: string;
}

interface OrderConfirmModalProps {
  orderList: OrderItem[];
  onConfirm: (orderData: any[]) => void;
}

const infoFields = [
  [
    { label: '联系人', key: 'contact' },
    { label: '联系电话', key: 'contactPhone' },
  ],
  [
    { label: '站点编号', key: 'stationNumber' },
    { label: '详细地址', key: 'address' },
  ],
];

const OrderConfirmModal = forwardRef<any, OrderConfirmModalProps>(
  ({ orderList = [], onConfirm }, ref) => {
    const [form] = Form.useForm();

    useImperativeHandle(ref, () => ({
      handleSubmit: async () => {
        try {
          const values = await form.validateFields();
          console.log('====================', values);
          const submitData = orderList.map((item, index) => ({
            requirementId: item.requirementId,
            expectedDeliveryMonth:
              values[`expectedDeliveryMonth_${index}`]?.format('YYYY-MM') || '',
          }));
          onConfirm && onConfirm(submitData);
        } catch (error) {
          console.error('表单验证失败:', error);
          throw error;
        }
      },
    }));

    const renderOrderCard = (item: OrderItem, index: number) => {
      return (
        <div key={item.requirementId} className="order-card">
          <div className="order-card-header">
            <div className="header-left">
              <CheckCircleFilled className="check-icon" />
              <span className="selected-text">
                已选择{item.vehicleModelName || '-'}
              </span>
            </div>
            <div className="header-right">
              <span className="info-item">用车数量：{item.count ?? '-'}</span>
              <Divider type="vertical" className="title-divider" />
              <span className="info-item">
                所属站点：{item.stationName || '-'}
              </span>
            </div>
          </div>

          <div className="delivery-month">
            <Form.Item
              name={`expectedDeliveryMonth_${index}`}
              label="预期交付月份"
              rules={[{ required: true, message: '请选择预期交付月份' }]}
            >
              <DatePicker
                placeholder="请选择月份"
                format="YYYY-MM"
                picker="month"
                minDate={dayjs()}
                maxDate={dayjs().add(12, 'months')}
              />
            </Form.Item>
          </div>

          <div className="order-info">
            {infoFields.map((row, rowIndex) => (
              <div key={rowIndex} className="info-row">
                {row.map((field) => (
                  <div key={field.key} className="info-col">
                    <span className="label">{field.label}：</span>
                    <span className="value">
                      {item[field.key as keyof OrderItem] || '-'}
                    </span>
                  </div>
                ))}
              </div>
            ))}
          </div>
          <Divider
            type="horizontal"
            style={
              index === orderList.length - 1
                ? { display: 'none' }
                : { margin: '36px 0 8px' }
            }
          />
        </div>
      );
    };

    return (
      <div className="order-confirm-modal">
        <div className="modal-header">
          <ExclamationCircleOutlined className="warning-icon" />
          <div className="header-title">是否确定下单需求？</div>
          {orderList.length > 1 && (
            <div className="header-subtitle">下单数：{orderList.length}个</div>
          )}
        </div>

        <Form form={form} layout="inline">
          <div className="order-list">
            {orderList.map((item, index) => renderOrderCard(item, index))}
          </div>
        </Form>
      </div>
    );
  },
);

export default OrderConfirmModal;
