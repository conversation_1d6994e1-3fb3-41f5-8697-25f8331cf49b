import React, { useEffect } from 'react';
import { ConfigProvider, message } from 'antd';
import zhCN from 'antd/lib/locale/zh_CN';
import baseRoutes from './routes';
import { useRoutes, useNavigate, useLocation } from 'react-router-dom';
import { RootState } from '@/redux/store';
import { useSelector, useDispatch } from 'react-redux';
import AllActionTypes from './redux/actionType';
import { actionSendHttpRequest } from './redux/action';
import { LoginState } from './utils/enum';
import '@/assets/css/index.scss';
import '@jd/x-coreui/lib/index.css';
import { HttpStatusCode } from './fetch/core/constant';

const App = () => {
  const dispatch = useDispatch();
  const navigator = useNavigate();
  const location = useLocation();
  const { hasLogin } = useSelector((state: RootState) => state.common);

  useEffect(() => {
    if (location.pathname === '/') {
      return;
    }
    if (location.pathname === '/login') {
      authenticateLogin();
    } else if (
      (location.pathname.includes('/app/') ||
        location.pathname.includes('/ota/')) &&
      hasLogin !== LoginState.LOGIN_SUCCESS
    ) {
      getUserInfo();
    }
  }, [location.pathname]);

  const router: any = useRoutes(baseRoutes);

  const authenticateLogin = () => {
    getUserInfo((res: any) => {
      const { menuData } = res.data;

      if (!menuData) {
        // 当前账号没有权限系统任何权限
        navigator('/login');
        return;
      }
      const firstPath: any = menuData[0]?.children[0]?.path;
      if (firstPath) {
        navigator(firstPath);
      } else {
        navigator('/login');
      }
    });
  };

  const getUserInfo = (cb?: Function) => {
    const requestData: HTTPFetchActionParams = {
      method: 'POST',
      path: '/authentication/getLoginUserInfo',
      body: {
        appCode: 'operation-platform-ui',
      },
      actionType: AllActionTypes.USER_INFO,
      nextActionFunc: (res: any) => {
        if (res?.code != HttpStatusCode.Success) {
          message.error('登录过期，请重新登录');
          navigator('/login');
        } else {
          handlePassportValidate(res, cb);
        }
      },
    };
    dispatch(actionSendHttpRequest(requestData));
  };

  const handlePassportValidate = (userInfo: any, cb?: Function) => {
    const loginParams: HTTPFetchActionParams = {
      absoluteURL: `${window.location.protocol}//${process.env.JDX_APP_FETCH_DATA_DOMAIN}/authentication/server/login/passport_login`,
      method: 'GET',
      urlParams: {
        appCode: 'operation-platform-ui',
      },
      actionType: AllActionTypes.AUTHENTICATE_LOGIN,
      nextActionFunc: (resp: any) => {
        if (resp.code !== HttpStatusCode.Success) {
          message.error(resp.message);
          navigator('/login');
          return;
        }
        cb && cb(userInfo);
      },
    };
    dispatch(actionSendHttpRequest(loginParams));
  };

  return (
    <ConfigProvider locale={zhCN} prefixCls="main-app">
      {router}
    </ConfigProvider>
  );
};

export default App;
