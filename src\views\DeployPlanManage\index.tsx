import React, { useEffect, useRef, useState } from 'react';
import {
  CommonForm,
  FormConfig,
  CommonTable,
  useTableData,
} from '@jd/x-coreui';
import { sortColumnsByState } from '@jd/x-coreui/lib/components/CommonTable/columnUtils';
import {
  SearchConfig,
  TableColumns,
  getDefaultColumnsState,
} from './utils/columns';
import { DeployPlan } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import { Button, message, Modal, Spin } from 'antd';
import {
  DeployPlanSession,
  DeployPlanType,
  DeployPlanStageStatus,
} from '@/utils/enum';
import DeployPlanDetail from './components/DeployPlanDetail';
import CreateDeployPlan from './components/CreateDeployPlan';

const StageStatusNameMap = new Map([
  [DeployPlanStageStatus.COMPLETED, '已完成'],
  [DeployPlanStageStatus.NOT_STARTED, '未开始'],
  [DeployPlanStageStatus.PROCESSING, '执行中'],
]);
const DeployPlanManage = () => {
  const fetchApi = new DeployPlan();
  const [modalShow, setModalShow] = useState<boolean>(false);
  const [planInfo, setPlanInfo] = useState<any>({});
  const [showCreatePlanModal, setShowCreatePlanModal] =
    useState<boolean>(false);
  const CreateDeployType = useRef<DeployPlanType | null>(null);
  const [tableKey, setTableKey] = useState<number>(0);
  // 列显示状态
  const [columnsState, setColumnsState] = useState(getDefaultColumnsState());
  const planStageOrderList = [
    DeployPlanSession.VEHICLE_CONFIRMATION,
    DeployPlanSession.VEHICLE_INSURANCE,
    DeployPlanSession.VEHICLE_ALLOCATION,
    DeployPlanSession.INITIATOR_VERIFICATION,
    DeployPlanSession.ROUTE_CONFIRMATION,
    DeployPlanSession.MAP_COLLECTION,
    DeployPlanSession.MAP_PRODUCTION,
    DeployPlanSession.ROUTE_TEST_RUN,
    DeployPlanSession.STATION_DELIVERY,
  ];
  const historySearchValues = useSelector(
    (state: RootState) => state.searchForm,
  );
  const initSearchCondition = {
    searchForm: {
      planNumber: null,
      planType: null,
      currentStage: null,
      stationBaseId: null,
      stateId: null,
      cityId: null,
      creatorErp: null,
      currentExecutorErp: null,
      stationInfo: null,
    },
    pageNum: 1,
    pageSize: 10,
  };
  const [searchCondition, setSearchCondition] = useState<SearchCondition>(
    () => {
      return historySearchValues.searchValues
        ? historySearchValues.searchValues
        : initSearchCondition;
    },
  );
  const { tableData, loading, reloadTable } = useTableData(
    {
      ...searchCondition.searchForm,
      pageNum: searchCondition.pageNum,
      pageSize: searchCondition.pageSize,
    },
    fetchApi.getDeploymentPlanList,
    tableKey,
  );
  const formateColumns = () => {
    const DeployPlanSessionList = Object.values(DeployPlanSession);
    return TableColumns.map((col) => {
      const colName = col.dataIndex.split('/')[0];
      if (DeployPlanSessionList.includes(colName)) {
        return {
          ...col,
          render: (text: any, record: any, index: number) => {
            const info = record.stageInfoList.filter(
              (v) => v.stageType === colName,
            )[0];
            return info ? (
              <span style={{ color: info.timeout ? '#FD4949' : '#696969' }}>
                {info.stageCompletedDate
                  ? info.stageCompletedDate
                  : StageStatusNameMap.get(info.stageStatus)
                  ? StageStatusNameMap.get(info.stageStatus)
                  : '-'}
              </span>
            ) : (
              '/'
            );
          },
        };
      }
      if (col.dataIndex === 'durationDays') {
        return {
          ...col,
          render: (text: any, record: any, index: number) => {
            return record.durationDays == undefined ? '-' : record.durationDays;
          },
        };
      }
      if (col.dataIndex === 'operation') {
        return {
          ...col,
          render: (text: any, record: any, index: number) => {
            return (
              <>
                <a
                  clstag="btn_check_detail"
                  onClick={() => {
                    getPlanInfo(record);
                  }}
                >
                  详情
                </a>
                {record.currentExecutorErp && (
                  <a
                    clstag="btn_urge"
                    onClick={() => {
                      urge(record);
                    }}
                  >
                    催办
                  </a>
                )}
              </>
            );
          },
        };
      }
      return {
        ...col,
        render: (text: any) => `${text || '-'}`,
      };
    });
  };

  const getPlanInfo = async (record) => {
    setModalShow(true);
    const res = await fetchApi.getDeploymentPlanDetail({
      planNumber: record.planNumber,
    });
    if (res.code === HttpStatusCode.Success) {
      const orderMap = new Map();
      res.data?.stageInfoList.forEach((v) => orderMap.set(v.stageType, v));
      setPlanInfo({
        ...res.data,
        stageInfoList: planStageOrderList.map((v) => orderMap.get(v)),
      });
    } else {
      message.error(res.message);
      setModalShow(false);
    }
  };

  const urge = async (record) => {
    const res = await fetchApi.urge({
      processInstanceId: record.processInstanceId,
    });
    if (res.code === HttpStatusCode.Success) {
      message.success('催办成功');
      reloadTable();
    } else {
      message.error(res.message);
    }
  };

  const onSearchClick = (val) => {
    const data = {
      searchForm: {
        ...val,
        stateId: val.stationInfo ? val.stationInfo[0] : null,
        stationBaseId: val.stationInfo ? val.stationInfo[2] : null,
        cityId: val.stationInfo ? val.stationInfo[1] : null,
      },
      pageNum: 1,
      pageSize: 10,
    };
    setSearchCondition(data);
    setTableKey(tableKey + 1);
  };
  const onResetClick = () => {
    setSearchCondition({ ...initSearchCondition });
    setTableKey(tableKey + 1);
  };

  const middleBtns: any[] = [
    {
      show: true,
      title: '创建补图计划',
      onClick: () => {
        CreateDeployType.current = DeployPlanType.MAP_SUPPLEMENT_PLAN;
        setShowCreatePlanModal(true);
      },
    },
    {
      show: true,
      title: '创建调车计划',
      onClick: () => {
        CreateDeployType.current = DeployPlanType.VEHICLE_ALLOCATION_PLAN;
        setShowCreatePlanModal(true);
      },
    },
  ];

  return (
    <>
      <CommonForm
        formConfig={SearchConfig}
        layout={'inline'}
        defaultValue={{ ...searchCondition.searchForm, fetchData: 'true' }}
        formType="search"
        colon={false}
        onSearchClick={onSearchClick}
        onResetClick={onResetClick}
      />
      <CommonTable
        tableListData={{
          list: tableData?.list ?? [],
          totalNumber: tableData?.total,
          totalPage: tableData?.pages,
        }}
        tableKey="deployPlanManageTable"
        columns={sortColumnsByState(formateColumns(), columnsState)}
        loading={loading}
        rowKey={'planNumber'}
        middleBtns={middleBtns}
        searchCondition={searchCondition}
        onPageChange={(value: any) => {
          setSearchCondition(value);
        }}
        // 列配置相关属性
        showColumnSetting={true}
        columnsState={{
          value: columnsState,
          onChange: setColumnsState,
          persistenceType: 'api',
          api: {
            useAbsoluteURL: true,
            fetchPath: `${location.protocol}//${process.env.JDX_APP_CLOUD_FETCH_DOMAIN}/k2/management/common/get_custom_filter_config`,
            savePath: `${location.protocol}//${process.env.JDX_APP_CLOUD_FETCH_DOMAIN}/k2/management/common/save_custom_filter_config`,
            LOPDN: process.env.JDX_APP_REQUEST_HEADER,
          },
        }}
        defaultColumnsState={getDefaultColumnsState()}
      />
      {modalShow && (
        <Modal
          title="计划详情"
          open={modalShow}
          width={'800px'}
          footer={[
            <Button key="back" onClick={() => setModalShow(false)}>
              关闭
            </Button>,
          ]}
          styles={{
            body: {
              maxHeight: '800px',
              overflowY: 'auto',
            },
          }}
          onCancel={() => setModalShow(false)}
        >
          {planInfo ? <DeployPlanDetail data={planInfo} /> : <Spin />}
        </Modal>
      )}

      {showCreatePlanModal && (
        <CreateDeployPlan
          showModal={showCreatePlanModal}
          planType={CreateDeployType.current!}
          closeModal={() => {
            CreateDeployType.current = null;
            reloadTable();
            setShowCreatePlanModal(false);
          }}
        />
      )}
    </>
  );
};

export default React.memo(DeployPlanManage);
