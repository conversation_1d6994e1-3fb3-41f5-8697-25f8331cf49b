// eslint-disable-next-line no-unused-vars
import { Form, FormInstance, message } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { CommonEdit, CommonForm, FieldItem, FormConfig } from '@/components';
import { formatLocation } from '@/utils/utils';
import { FactoryApi, CommonApi } from '@/fetch/business';
import { editFormConfig } from './utils/columns';
import cloneDeep from 'lodash/cloneDeep';
import { FactoryTitle, PageType } from '@/utils/EditTitle';
import { dropDownListKey, dropDownKey } from '@/utils/constant';
import { formatOptions } from '@/utils/utils';
import { HttpStatusCode } from '@/fetch/core/constant';

const VendorEdit = () => {
  const commonFetch = new CommonApi();
  const fetchApi = new FactoryApi();
  const navigator = useNavigate();
  const dispatch = useDispatch();
  const { id, type } = formatLocation(window.location.search);
  const [editForm] = Form.useForm();
  const [editFormFields, setEditFormFields] =
    useState<FormConfig>(editFormConfig);
  const formRef = useRef<FormInstance>();

  const [detailData, setDetailData] = useState<any>({
    enable: 1,
  });

  const vendorBreadCrumbItems = [
    {
      title: '厂商管理',
      route: '',
    },
    {
      title: FactoryTitle[type] || '',
      route: '',
    },
  ];

  useEffect(() => {
    formateFormConfig();
    setTimeout(() => {
      id && getDetail();
    }, 100);
  }, []);

  const getDetail = async () => {
    const res = await fetchApi.fetchFactoryDetail(id);
    if (res.code === HttpStatusCode.Success) {
      setDetailData(res.data);
    } else {
      message.error(res.message);
      navigator(-1);
    }
  };

  const formateFormConfig = async () => {
    const res = await commonFetch.getCommonDropDown({
      keyList: [
        dropDownKey.MANUFACTORY_PROPERTY,
        dropDownKey.HARDWARE_TYPE,
        dropDownKey.ENABLE,
      ],
    });
    const cityRes = await commonFetch.getCityDepartment();
    if (res.code === HttpStatusCode.Success) {
      const _fields = cloneDeep(editFormConfig.fields);
      const disabled = type === PageType.READONLY;
      _fields.forEach((v) => {
        switch (v.fieldName) {
          case 'combination':
            v.label = (
              <>
                <span style={{ color: 'red' }}>*</span>
                <span>{v.label}</span>
              </>
            );
            break;
          case 'property':
            v.options = formatOptions(
              res.data[dropDownListKey.MANUFACTORY_PROPERTY],
            );
            v.disabled = disabled;
            break;
          case 'hardwareTypeList':
            v.options = formatOptions(res.data[dropDownListKey.HARDWARE_TYPE]);
            v.disabled = disabled;
            break;
          case 'enable':
            v.options = formatOptions(res.data[dropDownListKey.ENABLE]);
            v.disabled = disabled;
            break;
          case 'cityInfo':
            v.options = cityRes.data;
            v.disabled = disabled;
            break;
          default:
            v.disabled = v.disabled ?? disabled;
        }
      });
      setEditFormFields({ ...editFormConfig, fields: _fields });
    }
  };

  const getFormInstance = (val) => {
    formRef.current = val;
  };
  const confirm = async () => {
    if (!formRef.current) {
      return;
    }
    const data = await formRef.current.validateFields();
    const res = await fetchApi.submitFactory({
      type,
      requestBody: {
        id: id,
        factoryName: data.factoryName,
        property: data.property,
        hardwareTypeList:
          data.property === 'VEHICLE_ASSEMBLY' ? [] : data.hardwareTypeList,
        address: data.address,
        cityId: data.cityInfo[1],
        enable: data.enable,
        person: data.person,
        contact: data.contact,
        email: data.email,
      },
    });
    if (res.code === HttpStatusCode.Success) {
      message.success(res.message);
      navigator('/app/factoryManage');
    } else {
      message.error(res.message);
    }
  };
  const goBack = () => {
    navigator('/app/factoryManage');
  };

  return (
    <CommonEdit
      title={FactoryTitle[type]}
      breadCrumbConfig={vendorBreadCrumbItems}
      onSubmitClick={confirm}
      onCancleClick={goBack}
      hideSubmit={type === PageType.READONLY}
      cancelTitle={type === PageType.READONLY ? '返回' : '取消'}
    >
      <CommonForm
        layout="horizontal"
        formConfig={editFormFields}
        defaultValue={{
          ...detailData,
          cityInfo:
            detailData.stateId && detailData.cityId
              ? [detailData.stateId, detailData.cityId]
              : [],
        }}
        getFormInstance={getFormInstance}
      />
      <Form.Item
        label=" "
        colon={false}
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 18 }}
      >
        <div style={{ color: '#808080' }}>
          <div>状态说明：</div>
          <div>
            1、只有“有效”的厂商，才能被引用成为一个厂商选择项，“无效”的厂商，不能被引用；
          </div>
          <div>
            2、厂商状态从“有效”改为“无效”，历史有被引用，历史数据不受影响，再修改，该无效厂商不在选择项范围内。
          </div>
        </div>
      </Form.Item>
    </CommonEdit>
  );
};

export default VendorEdit;
