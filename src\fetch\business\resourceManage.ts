import { YESNO } from '@/utils/enum';
import { request } from '../core';
import { PageType } from '@/utils/enum';

export class ResourceManageApi {
  // 分页查询数据列表
  fetchTableList({
    pageNum,
    pageSize,
    searchForm,
  }: {
    pageNum: number;
    pageSize: number;
    searchForm: Object;
  }) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/resource/permission_get_resource_page_list',
      body: { ...searchForm, pageNum, pageSize },
    };
    return request(options);
  }
  // 获取详情
  fetchDetail(resourceNumber: number | string) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/resource/permission_get_resource_detail',
      body: { resourceNumber },
    };
    return request(options);
  }

  //  新增编辑
  submitEditInfo({
    type,
    requestBody,
  }: {
    type: PageType.EDIT | PageType.ADD;
    requestBody: Object;
  }) {
    const options: RequestOptions = {
      method: 'POST',
      path:
        type === PageType.EDIT
          ? '/k2/management/resource/permission_edit_resource'
          : '/k2/management/resource/permission_add_resource',
      body: requestBody,
    };
    return request(options);
  }
  // 删除
  delResource(resourceNumber: number | string) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/resource/permission_delete_resource',
      body: { resourceNumber },
    };
    return request(options);
  }
}
