import React, { useCallback, useEffect, useState } from 'react';
import { CommonTable } from '@/components';
import { POINT_TYPE_MAP, POINT_TYPE_VALUE, columns } from './constants';
import { useTableData } from '@/components/CommonTable/useTableData';
import { PointsRequest, PointsResponse, UseStatus } from '@/types/resource';
import { DEFAULT_PAGE } from '@/utils/constant';
import { HttpStatusCode, Method } from '@/fetch/core/constant';
import { request } from '@/fetch/core';
import { Modal, Select, message } from 'antd';
import { useCommonDropDown } from '@/utils/hooks';
import { dropDownListKey, dropDownKey } from '@/utils/constant';
import { formatOptions } from '@/utils/utils';
import './index.scss';

const PointsTable = (props: { stationBaseId: number; warehouseNo: string }) => {
  const [tableKey, setTableKey] = useState<string>('0');
  const initSearchCondition: PointsRequest = {
    pointType: null,
    ...DEFAULT_PAGE,
  };
  const [searchCondition, setSearchCondition] =
    useState<any>(initSearchCondition);
  const [pointTypeOpt, setPointTypeOpt] = useState<
    {
      label: string;
      value: string;
    }[]
  >([]);
  const dropDown = useCommonDropDown([dropDownKey.WAREHOUSE_POINT_TYPE]);
  const fetchTable = useCallback(
    (searchOptions: PointsRequest) => {
      const { pageNum, pageSize, pointType } = searchOptions;
      const urlParams: any = { pageNum, pageSize };
      const requestOptions: RequestOptions = {
        path: '/k2/management/station_warehouse/get_map_point_page_list',
        method: Method.POST,
        body: { ...urlParams, stationBaseId: props.stationBaseId, pointType },
      };
      return request(requestOptions);
    },
    [searchCondition, tableKey],
  );

  useEffect(() => {
    const opts = formatOptions(
      dropDown[dropDownListKey.WAREHOUSE_POINT_TYPE] || [],
    );

    setPointTypeOpt(opts);
  }, [dropDown[dropDownListKey.WAREHOUSE_POINT_TYPE]]);
  const { tableData, loading } = useTableData<PointsRequest, PointsResponse>(
    searchCondition,
    fetchTable,
    tableKey,
  );

  const onChange = (value: any) => {
    setSearchCondition({
      ...searchCondition,
      pointType: value,
    });
  };

  return (
    <div className="points-table">
      <>
        点位类型：
        <Select
          placeholder="请选择"
          options={[{ label: '全部', value: '' }].concat(pointTypeOpt)}
          onChange={onChange}
        ></Select>
      </>
      <CommonTable
        tableListData={{
          list: tableData?.list || [],
          totalPage: tableData?.pages,
          totalNumber: tableData?.total,
        }}
        columns={columns}
        loading={loading}
        rowKey={'pointNo'}
        searchCondition={searchCondition}
        onPageChange={(paginationData: any) => {
          setSearchCondition({
            ...searchCondition,
            pageNum: paginationData.pageNum,
            pageSize: paginationData.pageSize,
          });
        }}
      />
    </div>
  );
};

export default React.memo(PointsTable);
