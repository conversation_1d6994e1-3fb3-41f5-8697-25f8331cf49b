import { request } from '@/fetch/core';
import { Method } from '@/fetch/core/constant';
import { ProductType } from '@/utils/enum';
class FetchApi {
  /**
   *  分页获取驾驶团队列表接口
   * @param {object} params
   * @param {number} params.pageNum
   * @param {number} params.pageSize
   * @param {string} params.cockpitTeamName
   * @return {Promise}
   */
  public fetchCockpitTeamInfoPageList = async (params: {
    pageNum: number;
    pageSize: number;
    cockpitTeamName?: string;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: `/k2/management/cockpit_team/cockpit_team_info_get_page_list`,
      body: {
        cockpitTeamName: params.cockpitTeamName,
        pageNum: params.pageNum,
        pageSize: params.pageSize,
      },
    };
    return request(requestOptions);
  };

  /**
   * 新建驾驶团队接口
   * @param {object} params
   * @param {string} params.cockpitTeamName
   * @param {Array} params.countryIdList
   * @param {Array} params.provinceIdList
   * @param {Array} params.cityIdList
   * @param {Array} params.stationIdList
   * @return {Promise}
   */
  public addCockpitTeam = async (params: {
    cockpitTeamName: string;
    countryIdList: any[];
    provinceIdList: any[];
    cityIdList: any[];
    stationIdList: any[];
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/k2/management/cockpit_team/add_cockpit_team',
      body: params,
    };
    return request(requestOptions);
  };

  /**
   * 获取驾驶团队信息接口
   * @param {string} cockpitTeamNumber
   * @return {Promise}
   */
  public getCockpitTeamInfo = async (
    cockpitTeamNumber: string,
  ): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/k2/management/cockpit_team/get_cockpit_team_info',
      body: {
        cockpitTeamNumber: cockpitTeamNumber,
      },
    };
    return request(requestOptions);
  };

  /**
   * 编辑驾驶舱信息接口
   * @param {object} params
   * @param {string} params.cockpitTeamNumber
   * @param {string} params.cockpitTeamName
   * @param {Array} params.countryIdList
   * @param {Array} params.provinceIdList
   * @param {Array} params.cityIdList
   * @param {Array} params.stationIdList
   * @return {Promise}
   */
  public editCockpitTeamInfo = async (params: {
    cockpitTeamName: string;
    cockpitTeamNumber: string;
    countryIdList: any[];
    provinceIdList: any[];
    cityIdList: any[];
    stationIdList: any[];
    userNameList: string[];
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/k2/management/cockpit_team/edit_cockpit_team_info',
      body: params,
    };
    return request(requestOptions);
  };

  /**
   * 变更驾驶团队状态接口
   * @param {object} params
   * @param {string} params.cockpitTeamNumber
   * @param {number} params.status
   * @return {Promise}
   */
  public changeCockpitTeamStatus = async (params: {
    cockpitTeamNumber: string;
    status: number;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/k2/management/cockpit_team/change_status',
      body: params,
    };
    return request(requestOptions);
  };

  /**
   * 查看驾驶团队已绑定的站点列表接口
   * @param {string} cockpitTeamNumber
   * @return {Promise}
   */
  public fetchCockpitTeamStationList = async (
    cockpitTeamNumber: string,
  ): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/k2/management/cockpit_team/get_bind_station_info',
      body: {
        cockpitTeamNumber: cockpitTeamNumber,
      },
    };
    return request(requestOptions);
  };
  /**
   * 获取全量的标准结构的站点四级地址接口
   * @param {object} params 请求参数
   * @param {number} params.countryId 国家Id
   * @param {Array} params.provinceIdList 省份Id列表
   * @param {Array} params.cityIdList 城市Id列表
   * @param {Array} params.stationIdList 站点Id列表
   * @param {Array} params.stationUseCaseList 站点用途列表
   * @param {string} params.stationType 站点类型
   * @return {Promise}
   */
  public fetchStandardLevelFourStation = (params: {
    provinceIdList: any[];
    cityIdList: any[];
    stationBaseIdList: any[];
    stationUseCaseList: any[];
    stationType: string;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/k2/management/station_base/standard_station_get_list',
      body: params,
    };

    return request(requestOptions);
  };

  getDepartmentOptions(params: any) {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/permission/web/common/get_current_down_list',
      body: params,
    };

    return request(requestOptions);
  }

  /** 获取权限系统人员列表 */
  public getAllUserInfo = async () => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/k2/management/common/get_all_user_info',
    };
    return request(requestOptions);
  };

  /** 获取用户真实手机号 */
  public getUserPhone = async (userName: string) => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/k2/management/common/get_user_phone',
      body: {
        userName: userName,
      },
    };
    return request(requestOptions);
  };

  /** 判断用户是否已有归属团队 */
  public fetchUserteam = async (userNameList: string[]) => {
    const requestOptions: RequestOptions = {
      method: Method.POST,
      path: '/k2/management/cockpit_team/is_user_in_team',
      body: {
        userNameList: userNameList,
      },
    };
    return request(requestOptions);
  };
}

export default FetchApi;
