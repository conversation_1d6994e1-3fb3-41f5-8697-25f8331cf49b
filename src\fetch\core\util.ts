function isString(opt: any) {
  return typeof opt === 'string';
}

export function serializeQueryParis(queryObj: any) {
  const paramsQuery = Object.entries(queryObj || {})
    .map((item) => {
      const [k, v]: any = item;
      return `${encodeURIComponent(k)}=${encodeURIComponent(v)}`;
    })
    .join('&');
  return paramsQuery;
}
/**
 * @param {RequestOptions} param 接收参数
 * @return {string}
 */
export const buildURL = (param: RequestOptions) => {
  let fullURL = '';
  if (param.absoluteURL) {
    fullURL = param.absoluteURL;
  } else {
    const baseURL =
      location.protocol + '//' + process.env.JDX_APP_CLOUD_FETCH_DOMAIN;
    const path = param.path || '';
    fullURL = new URL(path, baseURL).href;
  }
  if (param.urlParams) {
    const paramsQuery = serializeQueryParis(param.urlParams);
    fullURL = addQueryParisToURL(fullURL, paramsQuery);
  }
  return fullURL;
};

function addQueryParisToURL(url: string, queryParis: string) {
  let fullURL = url;
  const hashPos = fullURL.indexOf('#');
  let fullURLNoHash = hashPos > 0 ? fullURL.substring(0, hashPos) : fullURL;
  const hash = hashPos > 0 ? fullURL.substring(hashPos, fullURL.length) : '';
  if (fullURLNoHash.indexOf('?') === -1) {
    fullURLNoHash += '?';
  } else if (!fullURLNoHash.endsWith('&')) {
    fullURLNoHash += '&';
  }
  fullURLNoHash += queryParis;
  fullURL = fullURLNoHash;
  if (hashPos) {
    fullURL += hash;
  }
  return fullURL;
}

/**
 *
 * @param {RequestOptions} param 接收参数
 * @return {RequestInit}
 */
export const buildFetchInit = (param: RequestOptions) => {
  const toReturn: RequestInit = {};
  // ?? 当param.method值为null或undefind时保底值为POST，避免param.method为0或者空串时也被转成POST
  toReturn.method = param.method ?? 'POST';
  toReturn.credentials = 'include';
  toReturn.mode = 'cors';
  const headers: { [key: string]: string } = Object.assign(
    { 'LOP-DN': process.env.JDX_APP_REQUEST_HEADER },
    param.headers,
  );
  headers['Content-Type'] = 'application/json';
  headers['Content-Language'] = 'zh-CN,zh;q=0.9';
  if (param.method === 'POST' || param.method === 'PUT') {
    if (param.contentType) {
      headers['Content-Type'] = param.contentType;
    } else {
      headers['Content-Type'] =
        typeof param.body === 'string' ? 'text/plain' : 'application/json';
    }
  }
  if (param.newGeteway) {
    headers['LOP-DN'] = process.env.JDX_APP_REQUEST_HEADER!;
  }
  toReturn.headers = headers;
  const body = buildBody(param);
  if (body) {
    toReturn.body = body as string;
  }
  return toReturn;
};

/**
 *
 * @param {RequestOptions} param 请求参数
 * @return {string}
 */
export const buildBody = (param: RequestOptions) => {
  // 只处理POST和PUT请求的body数据
  if (!param.body) {
    return null;
  }

  if (
    param.method !== 'POST' &&
    param.method !== 'PUT' &&
    param.method !== 'DELETE'
  ) {
    return null;
  }

  if (param.contentType === 'application/x-www-form-urlencoded') {
    if (isString(param.body)) {
      return param.body;
    } else {
      return serializeQueryParis(param.body);
    }
  } else if (param.contentType === 'application/json') {
    if (isString(param.body)) {
      return param.body;
    } else {
      return JSON.stringify(param.body);
    }
  } else {
    return param.body;
  }
};

export async function parseResponseData(val: any, contentType?: string | null) {
  const ct = contentType || '';
  if (
    ct.indexOf('application/x-www-form-urlencoded') >= 0 ||
    ct.indexOf('text/plain') >= 0
  ) {
    const resp = val as Response;
    const txt = await resp.text();
    return txt;
  } else if (ct.indexOf('application/json') >= 0) {
    const resp = val as Response;
    const json = await resp.json();
    return json;
  } else {
    return val ? String(val) : '';
  }
}
