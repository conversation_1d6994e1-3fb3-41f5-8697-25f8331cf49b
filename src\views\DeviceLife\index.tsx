/* eslint-disable no-unused-vars */
import { Tabs } from 'antd';
import React, { useRef, useState } from 'react';
import './index.scss';
import { TabType } from './utils/constant';
import { CommonForm, FormConfig } from '@/components';
import { formatLocation, formatOptions } from '@/utils/utils';
import { useSelector, useDispatch } from 'react-redux';
import ProductionTab from './components/ProductionTab';
import ScheduleTab from './components/ScheduleTab';
import UsingTab from './components/UsingTab';
import RepairTab from './components/RepairTab/index';
import { RootState } from '@/redux/store';
import { removeSearchValues } from '@/redux/reducer/searchForm';
import { useCommonDropDown } from '@/utils/hooks';
import isEqual from 'lodash/isEqual';
const { TabPane } = Tabs;
const VehicleManagement = () => {
  const { tabList } = useSelector((state: RootState) => state.common);
  const searchRef = useRef<HTMLDivElement>(null);
  const dispatch = useDispatch();
  const urlData: any = formatLocation(window.location.search);
  const [resetKey, setResetKey] = useState(0);
  const [tableKey, setTableKey] = useState('');

  // 获取当前页面路径
  const currentPath = window.location.pathname || '';

  // 根据当前路径获取对应的 tabList
  const currentTabList = tabList[currentPath] || [];
  const historyActiveTabKey: any = useSelector(
    (state: RootState) => state.activeTabKey,
  );
  const [activeTabKey, setActiveTabKey] = useState<any>(() => {
    return historyActiveTabKey.activeTabKey
      ? historyActiveTabKey.activeTabKey
      : TabType.VEHICLE_PRODUCTION;
  });
  const [click, setClick] = useState<number>(0);
  const initSearchCondition = {
    searchForm: {
      stationBaseId: null,
      stateId: null,
      cityId: null,
      deviceTypeBaseId: null,
      productType: null,
      name: urlData.name ? urlData.name : null,
      businessType: null,
      ownerUseCaseList: [],
      checkStatus: null,
      hardwareStatusList: [],
      isRequire: null,
      isVirtual: null,
      serialNo: null,
    },
    pageNum: 1,
    pageSize: 10,
  };
  const [searchCondition, setSearchCondition] = useState<{
    searchForm: any;
    pageNum: number;
    pageSize: number;
  }>(initSearchCondition);
  const dropdownData = useCommonDropDown([
    'VEHICLE_HARDWARE_STATUS',
    'VEHICLE_BUSINESS_TYPE',
    'YES_OR_NO',
    'PRODUCT_TYPE',
    'VEHICLE_OWNER_USE_CASE',
    'VEHICLE_TYPE',
    'VEHICLE_CHECK_STATUS',
  ]);

  const searchConfig: FormConfig = {
    fields: [
      {
        fieldName: 'stationInfo',
        label: '省市站',
        placeholder: '请选择省市站信息',
        type: 'cascader',
        mapRelation: { label: 'name', value: 'id', children: 'children' },
        specialFetch: 'station',
      },
      {
        fieldName: 'serialNo',
        label: '车架号',
        placeholder: '请输入车架号',
        type: 'input',
      },
      {
        fieldName: 'name',
        label: '车牌号',
        placeholder: '请输入车牌号',
        type: 'input',
      },
      {
        fieldName: 'hardwareStatusList',
        label: '车辆生命周期',
        placeholder: '请选择车辆生命周期',
        type: 'select',
        labelInValue: false,
        multiple: true,
        showSearch: true,
        options: formatOptions(dropdownData.vehicleHardwareStatusList),
      },
      {
        fieldName: 'productType',
        label: '产品类型',
        placeholder: '请选择产品类型',
        type: 'select',
        labelInValue: false,
        options: formatOptions(dropdownData.productTypeList),
      },
      {
        fieldName: 'businessType',
        label: '设备类型',
        placeholder: '请选择设备类型',
        type: 'select',
        labelInValue: false,
        options: formatOptions(dropdownData.vehicleBusinessTypeList),
      },
      {
        fieldName: 'ownerUseCaseList',
        label: '车辆归属方',
        placeholder: '请选择车辆归属方',
        type: 'select',
        multiple: true,
        showSearch: true,
        labelInValue: false,
        options: formatOptions(dropdownData.vehicleOwnerUseCaseList),
      },
      {
        fieldName: 'isRequire',
        label: '存在未完成维修单',
        placeholder: '请选择',
        type: 'select',
        showSearch: true,
        labelInValue: false,
        options: formatOptions(dropdownData.yesOrNoList),
      },
      {
        fieldName: 'isVirtual',
        label: '是否为虚拟车',
        placeholder: '请选择',
        type: 'select',
        showSearch: true,
        labelInValue: false,
        options: formatOptions(dropdownData.yesOrNoList),
      },
      {
        fieldName: 'deviceTypeBaseId',
        label: '车型名称',
        placeholder: '请输入车型名称，支持关键字联想全称',
        type: 'select',
        showSearch: true,
        labelInValue: false,
        options: formatOptions(dropdownData.deviceTypeBaseInfoList),
      },
      {
        fieldName: 'checkStatus',
        label: '标定检测状态',
        placeholder: '请选择状态',
        type: 'select',
        labelInValue: false,
        options: formatOptions(dropdownData.vehicleCheckStatusList),
      },
    ],
  };
  const handleChangeActiveTab = (key: any) => {
    setActiveTabKey(key);
  };

  const onSearchClick = (values) => {
    const data = {
      searchForm: {
        ...values,
        stateId:
          values.stationInfo && values.stationInfo.length > 0
            ? values.stationInfo[0]
            : null,
        cityId:
          values.stationInfo && values.stationInfo.length >= 2
            ? values.stationInfo[1]
            : null,
        stationBaseId:
          values.stationInfo && values.stationInfo.length === 3
            ? values.stationInfo[2]
            : null,
      },
      pageSize: 10,
      pageNum: 1,
    };
    delete data.searchForm.stationInfo;
    if (isEqual(data, searchCondition)) {
      setTableKey(new Date().getMilliseconds().toString());
    } else {
      setSearchCondition(data);
    }
    setClick(click + 1);
  };
  const onResetClick = () => {
    const search = {
      searchForm: {
        stateId: null,
        cityId: null,
        stationBaseId: null,
        deviceTypeBaseId: null,
        productType: null,
        name: null,
        businessType: null,
        ownerUseCaseList: [],
        checkStatus: null,
        hardwareStatusList: [],
        isRequire: null,
        isVirtual: null,
        serialNo: null,
        stationInfo: null,
      },
      pageNum: 1,
      pageSize: 10,
    };
    dispatch(
      removeSearchValues({
        routeName: null,
        searchValues: search,
      }),
    );
    setSearchCondition({ ...search });
    setClick(click + 1);
  };
  console.log(currentTabList);
  return (
    <div className="vehicle-management">
      <div ref={searchRef}>
        <CommonForm
          formConfig={searchConfig}
          defaultValue={searchCondition.searchForm}
          layout="inline"
          formType="search"
          colon={false}
          onSearchClick={onSearchClick}
          onResetClick={onResetClick}
        />
      </div>

      <div className="card-container">
        <Tabs
          activeKey={activeTabKey}
          onChange={handleChangeActiveTab}
          type="card"
        >
          {currentTabList.length > 0
            ? // 对 currentTabList 按 orderBy 排序后再渲染
              [...currentTabList]
                .sort((a, b) => {
                  // 如果 orderBy 不存在，则默认为 0
                  const orderA = a.orderBy !== undefined ? a.orderBy : 0;
                  const orderB = b.orderBy !== undefined ? b.orderBy : 0;
                  return orderA - orderB; // 升序排列
                })
                .map((item: any) => {
                  return <TabPane tab={item.name} key={item.code}></TabPane>;
                })
            : // 如果当前路径没有对应的 tabList，则使用所有 tabList 的所有项目
              Object.values(tabList)
                .flat()
                .sort((a: any, b: any) => {
                  // 如果 orderBy 不存在，则默认为 0
                  const orderA = a.orderBy !== undefined ? a.orderBy : 0;
                  const orderB = b.orderBy !== undefined ? b.orderBy : 0;
                  return orderA - orderB; // 升序排列
                })
                .map((item: any) => {
                  return <TabPane tab={item.name} key={item.code}></TabPane>;
                })}
        </Tabs>
      </div>
      <div className="manage-table" key={resetKey}>
        {activeTabKey === TabType.VEHICLE_SCHEDULE && (
          <ScheduleTab
            searchForm={searchCondition}
            activeTabKey={activeTabKey}
            click={click}
            setSearchForm={setSearchCondition}
            searchRef={searchRef}
            tableKey={tableKey}
            setTableKey={setTableKey}
          />
        )}
        {activeTabKey === TabType.VEHICLE_PRODUCTION && (
          <ProductionTab
            searchForm={searchCondition}
            activeTabKey={activeTabKey}
            click={click}
            setSearchForm={setSearchCondition}
            searchRef={searchRef}
            tableKey={tableKey}
            setTableKey={setTableKey}
          />
        )}
        {activeTabKey === TabType.VEHICLE_USING && (
          <UsingTab
            searchForm={searchCondition}
            activeTabKey={activeTabKey}
            click={click}
            setSearchForm={setSearchCondition}
            searchRef={searchRef}
            tableKey={tableKey}
            setTableKey={setTableKey}
          />
        )}
        {activeTabKey === TabType.VEHICLE_REPAIRING && (
          <RepairTab
            searchForm={searchCondition}
            activeTabKey={activeTabKey}
            click={click}
            setSearchForm={setSearchCondition}
            searchRef={searchRef}
            tableKey={tableKey}
            setTableKey={setTableKey}
          />
        )}
      </div>
    </div>
  );
};

export default React.memo(VehicleManagement);
