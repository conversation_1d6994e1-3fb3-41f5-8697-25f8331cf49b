import { YESNO } from '@/utils/enum';
import { request } from '../core';
import { PageType } from '@/utils/enum';
export class ErrorCodeConfApi {
  // 分页查询数据列表
  fetchTableList({
    pageNum,
    pageSize,
    searchForm,
  }: {
    pageNum: number;
    pageSize: number;
    searchForm: Object;
  }) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/error_code_translate/error_code_translate_info_get_page_list',
      body: { ...searchForm, pageNum, pageSize },
    };
    return request(options);
  }
  //  新增编辑
  submitInfo({
    type,
    requestBody,
  }: {
    type: PageType.EDIT | PageType.ADD;
    requestBody: Object;
  }) {
    const options: RequestOptions = {
      method: 'POST',
      path:
        type === PageType.EDIT
          ? '/k2/management/error_code_translate/edit_error_code_translate_info'
          : '/k2/management/error_code_translate/add_error_code_translate_info',
      body: requestBody,
    };
    return request(options);
  }
  // 获取详情
  fetchDetail(id: number) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/error_code_translate/get_error_code_translate_info',
      body: { id },
    };
    return request(options);
  }
  // 变更状态
  updateStatus(id: number) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/error_code_translate/del_error_code_translate_info',
      body: { id },
    };
    return request(options);
  }
}
