import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { message, Form, Popconfirm, Table, Button } from 'antd';
import { useNavigate } from 'react-router-dom';
import {
  tableConfigData,
  searchConfig,
  viewBindedVehicleColumns,
} from './utils/column';
import PackageTable from '@/components/PackageTable';
import showModal from '@/components/CommonModal';
import Searchform from '@/components/SearchForm';
import {
  saveSearchValues,
  removeSearchValues,
} from '@/redux/reducer/searchForm';
import { HttpStatusCode } from '@/fetch/core/constant';
import FetchApi from './utils/fetchApi';
import './index.scss';
import  isNull from 'lodash/isNull'
import isUndefined  from 'lodash/isUndefined';
import { RootState } from '@/redux/store';
const CockpitManagement = () => {
  const fetchApi = new FetchApi();
  const navigator = useNavigate();
  const [loading, setLoading] = useState<boolean>(false);
  const [formRef] = Form.useForm();
  const dispatch = useDispatch();
  const historySearchValue: any = useSelector(
    (state: RootState) => state.searchForm,
  );

  const initSearchCondition = {
    searchForm: {
      cockpitNumber: null,
      cockpitType: null,
      cockpitTeamNumber: null,
    },
    page: 1,
    size: 10,
  };
  const [dropDownList, setDropDownList] = useState<any>({
    cockpitTeamNumber: [],
  });
  const [searchCondition, setSearchCondition] = useState<{
    searchForm: any;
    page: number;
    size: number;
  }>(() => {
    return historySearchValue.searchValues
      ? historySearchValue.searchValues
      : initSearchCondition;
  });
  const [tableListData, setTableListData] = useState<{
    list: any[];
    totalNumber: number;
    totalPage: number;
  }>({
    list: [],
    totalNumber: 0,
    totalPage: 0,
  });

  useEffect(() => {
    getTableList(searchCondition);
    getCockpitTeamList();
  }, []);
  const middleBtns: any[] = [
    {
      show: true,
      title: '新建',
      key: 'cockpitManagementAdd',
      onClick: () => navigator(`/app/cockpitmanagement/add`),
    },
  ];

  const deleteCockpit = async (cockpitNumber: string) => {
    try {
      const res = await fetchApi.deleteCockpit(cockpitNumber);
      if (res.code === HttpStatusCode.Success) {
        message.success('删除座席成功');
        getTableList(searchCondition);
      } else {
        message.error(res.message || '接口请求错误');
      }
    } catch (err: any) {
      message.error(err);
    }
  };

  const viewApiKey = async (cockpitNumber: string, cockpitName: string) => {
    try {
      const res = await fetchApi.getAPIKey({ cockpitNumber });
      if (res.code === HttpStatusCode.Success) {
        showModal({
          title: `查看密钥 - 【${cockpitName}】`,
          content: res.data.apiKey,
          footer: {
            showCancel: true,
            cancelText: '关闭',
            cancelFunc: (cb) => {
              cb();
            },
          },
        });
      }
    } catch (err: any) {
      message.error(err);
    }
  };

  const formatColumns = (columns: any) => {
    return columns.map((item: any) => {
      switch (item.dataIndex) {
        case 'rowIndex':
          return {
            ...item,
            render: (text: any, record: any, index: number) => {
              return `${
                (searchCondition.page - 1) * searchCondition.size + index + 1
              }`;
            },
          };
        case 'operation':
          return {
            ...item,
            render: (text: any, record: any, index: number) => {
              return (
                <div className="operate">
                  <a
                    className="operate-btn"
                    onClick={() => {
                      navigator(
                        `/app/cockpitmanagement/edit/${record.cockpitNumber}`,
                      );
                    }}
                  >
                    编辑
                  </a>
                  <Popconfirm
                    title="确定是否要删除吗？"
                    okText="确定"
                    cancelText="取消"
                    onConfirm={() => deleteCockpit(record.cockpitNumber)}
                  >
                    <a className="operate-btn">删除</a>
                  </Popconfirm>
                  <a
                    className="operate-btn"
                    onClick={() =>
                      viewApiKey(record.cockpitNumber, record.cockpitName)
                    }
                  >
                    查看密钥
                  </a>
                  <a
                    className="operate-btn"
                    onClick={() =>
                      viewBindedVehicle(
                        record.cockpitNumber,
                        record.cockpitName,
                      )
                    }
                  >
                    查看已绑车辆
                  </a>
                </div>
              );
            },
          };
        default:
          return {
            ...item,
            render: (text: any) => `${text || '-'}`,
          };
      }
    });
  };

  const formateBindedVehicleColumns = () => {
    return viewBindedVehicleColumns.map((item) => {
      switch (item.dataIndex) {
        case 'order':
          return {
            ...item,
            render: (text: any, record: any, index: number) => {
              return `${index + 1}`;
            },
          };
        default:
          return {
            ...item,
            render: (text: any) => `${text || '-'}`,
          };
      }
    });
  };

  const getCockpitTeamList = async () => {
    const res = await fetchApi.fetchCockpitTeamList();
    if (res.code === HttpStatusCode.Success && res.data) {
      setDropDownList({
        ...dropDownList,
        cockpitTeamNumber: res.data.map((item: any) => {
          return {
            label: item.cockpitTeamName,
            value: item.cockpitTeamNumber,
          };
        }),
      });
    }
  };
  const getTableList = async (searchCondition: any) => {
    try {
      setLoading(true);
      const params = {
        pageNum: searchCondition.page,
        pageSize: searchCondition.size,
        cockpitNumber: searchCondition.searchForm.cockpitNumber,
        cockpitType:
          isNull(searchCondition.searchForm.cockpitType) ||
          isUndefined(searchCondition.searchForm.cockpitType)
            ? null
            : searchCondition.searchForm.cockpitType.value,
        cockpitTeamNumber:
          isNull(searchCondition.searchForm.cockpitTeamNumber) ||
          isUndefined(searchCondition.searchForm.cockpitTeamNumber)
            ? null
            : searchCondition.searchForm.cockpitTeamNumber.value,
      };
      const res = await fetchApi.fetchCockpitInfoPageList(params);
      if (res.code === HttpStatusCode.Success) {
        setTableListData({
          list: res.data.list,
          totalNumber: res.data.total,
          totalPage: res.data.pages,
        });
      }
    } catch (e: any) {
      message.error(e);
    } finally {
      setLoading(false);
    }
  };

  const viewBindedVehicle = async (
    cockpitNumber: string,
    cockpitName: string,
  ) => {
    const res = await fetchApi.getBindedVehicle(cockpitNumber);
    if (res.code === HttpStatusCode.Success) {
      showModal({
        title: cockpitName,
        content: (
          <Table
            pagination={false}
            bordered
            columns={formateBindedVehicleColumns()}
            dataSource={res.data}
            rowKey={'vehicleName'}
          />
        ),
        footer: {
          showCancel: true,
          cancelText: '关闭',
          cancelFunc: (cb) => {
            cb();
          },
        },
      });
    }
  };

  const onSearchClick = () => {
    const values = formRef.getFieldsValue();
    const data = {
      searchForm: values,
      size: 10,
      page: 1,
    };
    setSearchCondition(data);
    getTableList(data);
    dispatch(
      saveSearchValues({
        routeName: location.pathname,
        searchValues: data,
      }),
    );
  };

  const onResetClick = () => {
    formRef.setFieldsValue(initSearchCondition.searchForm);
    dispatch(
      removeSearchValues({
        routeName: null,
        searchValues: initSearchCondition,
      }),
    );
    setSearchCondition({ ...initSearchCondition });
    getTableList(initSearchCondition);
  };

  const onPageChange = (value: any) => {
    dispatch(
      saveSearchValues({
        routeName: location.pathname,
        searchValues: value,
      }),
    );
    setSearchCondition(value);
    getTableList(value);
  };
  return (
    <div className="drive-cabin-manage">
      <div className="search-form">
        <Searchform
          dropDownMap={dropDownList}
          configData={searchConfig}
          onSearchClick={onSearchClick}
          onResetClick={onResetClick}
          initValues={searchCondition.searchForm}
          formRef={formRef}
          key={'driveCabinManagementSearchform'}
        />
      </div>
      <PackageTable
        key={'driveCabinManagementTable'}
        loading={loading}
        columns={formatColumns(tableConfigData)}
        tableListData={tableListData}
        rowKey={'id'}
        middleBtns={middleBtns}
        searchCondition={searchCondition}
        onPageChange={(value: any) => onPageChange(value)}
      />
    </div>
  );
};
export default React.memo(CockpitManagement);
