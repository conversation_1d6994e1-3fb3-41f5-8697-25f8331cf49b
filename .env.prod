NODE_ENV=production
PUBLIC_URL=/
port=3000
#变量以JDX_APP_ 开头定义
JDX_APP_DOMAIN=jdl.cn;
JDX_APP_OTA_URL=https://jdxota-ui.jdl.cn
JDX_APP_MAP_EDITOR=jdxgateway.jdl.cn
JDX_APP_BI_URL=https://bi.jdl.cn
JDX_APP_MAP_DATA=product

JDX_APP_LOCATION_HOST = 'jdxvehicle-ui.jdl.cn'
JDX_APP_FETCH_DATA_DOMAIN=jdxgateway.jdl.cn

JDX_APP_PASSPORT_LOGIN_HOST = 'https://sso.jdl.cn/sso/login?ReturnUrl='
JDX_APP_JDL_LOGOUT_HOST = 'https://sso.jdl.com/exit?callback=?'

#跳转地图组链接
JDX_APP_MAP_GROUP=https://out-data-task.jd.com/#/warehouseBlueprint
JDX_APP_CLOUD_FETCH_DOMAIN=api-lop.jdl.cn
JDX_APP_REQUEST_HEADER=jdx.management.public.jsf.product
JDX_APP_UPLOAD_PRE_SIGNATURE_URL=https://api-lop.jdl.cn/infrastructure/oss/getPreUrl
JDX_APP_MAP_EDITOR=jdxgateway.jdl.cn
JDX_APP_MAP_DATA = product
