/* eslint-disable new-cap */
/* eslint-disable react/display-name */
/* eslint-disable react/no-unescaped-entities */
/* eslint-disable no-unused-vars */

import { Modal, Tabs, DatePicker, Form, Row, Col } from 'antd';
import React, { useEffect, useState } from 'react';
import CustomTable from '../CustomTable';
import { CustomButton, ButtonType } from '@/components';
import './index.scss';
import { Dayjs } from 'dayjs';
const { TabPane } = Tabs;
const { RangePicker } = DatePicker;
const UploadHistoryModal = ({
  visable,
  onClose,
}: {
  visable: boolean;
  onClose: Function;
}) => {
  const [activeKey, setActiveKey] = useState('1');
  const [searchFormValue, setSearchFormValue] = useState<{
    startTime: null | string;
    endTime: null | string;
  }>({
    startTime: null,
    endTime: null,
  });

  const [searchForm] = Form.useForm();
  return (
    <Modal
      width="50vw"
      maskClosable={false}
      title="导入日志"
      footer={null}
      closable
      onCancel={() => {
        onClose && onClose();
      }}
      visible={visable}
    >
      <div className="upload-history-modal">
        <div>
          <Form form={searchForm}>
            <Row>
              <Col span={20}>
                <Form.Item name="operatTime" label="操作时间">
                  <RangePicker
                    showTime={{ format: 'HH:mm' }}
                    format="YYYY-MM-DD HH:mm"
                  />
                </Form.Item>
              </Col>
              <Col>
                <CustomButton
                  title="查询"
                  height={30}
                  onSubmitClick={() => {
                    const dataPicker: any =
                      searchForm.getFieldValue('operatTime');
                    let startTime;
                    let endTime;
                    if (dataPicker && dataPicker.length > 0) {
                      const startMoment: Dayjs = dataPicker[0];
                      if (startMoment) {
                        startTime = startMoment.format('YYYY-MM-DD HH:mm:ss');
                      }
                      const endMoment: Dayjs = dataPicker[1];
                      if (endMoment) {
                        endTime = endMoment.format('YYYY-MM-DD HH:mm:ss');
                      }
                    }
                    const search = {
                      startTime,
                      endTime,
                    };
                    setSearchFormValue(search);
                  }}
                />
              </Col>
            </Row>
          </Form>
        </div>
        <Tabs
          type="card"
          onChange={(activeKey) => {
            setActiveKey(activeKey);
          }}
          tabBarGutter={8}
          activeKey={activeKey}
        >
          <TabPane tab="批量新增车辆" key="1">
            <CustomTable
              searchFormValue={searchFormValue}
              moduleName="vehicle"
            />
          </TabPane>
          <TabPane tab="批量导入更多设备号" key="2">
            <CustomTable
              searchFormValue={searchFormValue}
              moduleName="cardNo"
            />
          </TabPane>
        </Tabs>
      </div>
    </Modal>
  );
};

export default React.memo(UploadHistoryModal);
