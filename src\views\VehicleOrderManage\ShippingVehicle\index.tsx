import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Card, Divider, Button, message } from 'antd';
import { UnorderedListOutlined } from '@ant-design/icons';
import { CommonForm } from '@jd/x-coreui';
import { BreadCrumb } from '@/components';
import { vehicleOrderManageApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';
import dayjs from 'dayjs';
import './index.scss';
import { shippingFormConfig, detailFormConfig } from '../utils/constant';

const ShippingVehicle: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [detailData, setDetailData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const shippingFormRef = useRef<any>(null);

  // 从URL获取参数
  const orderId = searchParams.get('id');
  const orderCount = searchParams.get('count');
  const vehicleModel = searchParams.get('vehicleModel');
  const allocationCount = searchParams.get('allocationCount');

  // 面包屑配置
  const breadcrumbItems = [
    {
      title: '订单管理',
      route: '/app/vehicleOrderManage',
    },
    {
      title: '新增发运信息',
      route: '',
    },
  ];

  useEffect(() => {
    if (orderId) {
      loadOrderDetail();
    }
  }, [orderId]);

  // 加载订单详情
  const loadOrderDetail = async () => {
    if (!Number(orderId)) return;
    setLoading(true);
    try {
      const res = await vehicleOrderManageApi.getDetailById(Number(orderId));
      if (res && res.code === HttpStatusCode.Success && res.data) {
        setDetailData(res.data);
      } else {
        message.error(res.message || '获取订单详情失败');
      }
    } catch (error) {
      console.error('加载订单详情失败:', error);
      message.error('加载订单详情失败');
    } finally {
      setLoading(false);
    }
  };

  // 重置表单
  const handleReset = () => {
    if (shippingFormRef.current) {
      shippingFormRef.current.resetFields();
    }
  };

  // 提交发运信息
  const handleSubmit = async () => {
    if (!shippingFormRef.current || !Number(orderId)) {
      message.error('表单或订单信息异常');
      return;
    }

    try {
      const values = await shippingFormRef.current.validateFields();
      setSubmitting(true);
      const submitData = {
        ...values,
        id: Number(orderId),
        estimatedArrivalTime: values.estimatedArrivalTime
          ? dayjs(values.estimatedArrivalTime).format('YYYY-MM-DD')
          : undefined,
      };
      const response = await vehicleOrderManageApi.dispatchVehicle(submitData);
      if (response.code === HttpStatusCode.Success) {
        message.success('发运信息提交成功');
        navigate('/app/vehicleOrderManage');
      } else {
        message.error(response.message || '提交失败');
      }
    } catch (error: any) {
      if (error.errorFields) {
        message.error('请检查表单填写是否正确');
      } else {
        console.error('提交发运信息失败:', error);
        message.error('提交失败');
      }
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="shipping-vehicle">
      <div style={{ padding: '15px 0' }}>
        <BreadCrumb items={breadcrumbItems} />
      </div>
      <Card className="card">
        <div className="info-content">
          <div className="info">
            订单数为&nbsp;<span className="highlight">{orderCount}</span>
            &nbsp;
          </div>
          <div className="info">
            配车数为&nbsp;
            <span className="highlight">{allocationCount}</span>
          </div>
          <Divider
            type="vertical"
            className="info-divider"
            style={{ margin: '0 8px' }}
          />
          <div className="info">
            车型为&nbsp;<span className="highlight">{vehicleModel}</span>
            ，请为该订单填写发运信息
          </div>
        </div>
      </Card>
      <Card className="card" loading={loading}>
        <div className="card-header">
          <UnorderedListOutlined className="header-icon" />
          <span className="header-title">核实收件信息</span>
        </div>
        <div className="card-content">
          <CommonForm
            formConfig={detailFormConfig}
            defaultValue={{
              ...detailData,
              vehicleNameList:
                detailData?.vehicleNameList
                  ?.map((vehicle: any) => vehicle.vehicleName)
                  ?.join('、') || '-',
            }}
            layout="inline"
            formType="edit"
          />
        </div>
      </Card>
      <Card className="card">
        <div className="card-header">
          <UnorderedListOutlined className="header-icon" />
          <span className="header-title">填写发运信息</span>
        </div>
        <div className="card-content">
          <CommonForm
            formConfig={shippingFormConfig}
            layout="inline"
            formType="edit"
            getFormInstance={(ref) => {
              shippingFormRef.current = ref;
            }}
          />
        </div>
      </Card>
      <div className="bottom-actions">
        <Button onClick={handleReset}>重置</Button>
        <Button type="primary" loading={submitting} onClick={handleSubmit}>
          提交
        </Button>
      </div>
    </div>
  );
};

export default React.memo(ShippingVehicle);
