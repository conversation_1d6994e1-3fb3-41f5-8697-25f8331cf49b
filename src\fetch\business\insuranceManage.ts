import { request } from '../core';

// 类型定义
export interface InsuranceAttachment {
  type: string;
  fileKey: string | null;
  bucketName: string | null;
  url?: string | null;
}

export interface DeviceInsuranceInfo {
  deviceName: string;
  serialNo: string;
  provinceName: string;
  cityName: string;
  stationName: string;
  radius: number;
  deviceInsuredStatus?: string;
  remark?: string;
  vheicleInsuranceId?: number;
}

export interface InsuranceSearchForm {
  pageNum: number;
  pageSize: number;
  policyNumber?: string | null;
  deviceName?: string | null;
  insuredStatus?: string | null;
  effectiveStartTime?: Date | null;
  effectiveEndTime?: Date | null;
}

export interface DeviceInsuranceSearchForm {
  pageNum: number;
  pageSize: number;
  insuranceId: number;
}

export interface InsuranceDetail {
  insuranceId: number;
  policyNumber: string;
  insuranceCompany: string;
  insuranceType: string;
  insuredEntity: string;
  policyCoverageAmount: number;
  effectiveStartTime: Date;
  effectiveEndTime: Date;
  policyAttachment: InsuranceAttachment;
  otherAttachmentList?: InsuranceAttachment[];
  deviceInsuranceInfoList: DeviceInsuranceInfo[];
  remark?: string;
}

export interface DeviceInfo {
  deviceName: string;
  serialNo: string;
  stateName: string;
  cityName: string;
  stationName: string;
}

export interface Station {
  stationBaseId: number;
  stationName: string;
}

const fetchDomain = `${location.protocol}//${process.env.JDX_APP_CLOUD_FETCH_DOMAIN}`;
const headers = {
  'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
};
class InsuranceManage {
  // 分页获取保险列表
  getInsurancePageList(params: InsuranceSearchForm) {
    const options: RequestOptions = {
      method: 'POST',
      path: `${fetchDomain}/k2/management/insurance_info/get_page_list`,
      body: params,
      headers,
    };
    return request(options);
  }

  // 分页获取投保车辆明细
  getDeviceInsurancePageList(params: DeviceInsuranceSearchForm) {
    const options: RequestOptions = {
      method: 'POST',
      path: `${fetchDomain}/k2/management/insurance_info/get_device_insurance_page_list`,
      body: params,
      headers,
    };
    return request(options);
  }

  // 新建车辆保险
  addInsurance(params: Omit<InsuranceDetail, 'insuranceId'>) {
    const options: RequestOptions = {
      method: 'POST',
      path: `${fetchDomain}/k2/management/insurance_info/add`,
      body: params,
      headers,
    };
    return request(options);
  }

  // 获取车辆保险详情
  getInsuranceDetail(insuranceId: number) {
    const options: RequestOptions = {
      method: 'POST',
      path: `${fetchDomain}/k2/management/insurance_info/get_insurance_detail`,
      body: { insuranceId },
      headers,
    };
    return request(options);
  }

  // 编辑车辆保险
  editInsurance(params: InsuranceDetail) {
    const options: RequestOptions = {
      method: 'POST',
      path: `${fetchDomain}/k2/management/insurance_info/edit`,
      body: params,
      headers,
    };
    return request(options);
  }

  // 获取车辆详情信息
  getDeviceInfoList(params: { stationBaseId?: number; deviceName?: string }) {
    const options: RequestOptions = {
      method: 'POST',
      path: `${fetchDomain}/k2/management/device/get_device_address_brief_info_list`,
      body: params,
      headers,
    };
    return request(options);
  }

  // 获取站点列表
  getStationList() {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/station_base/get_station_address_list',
      body: {},
    };
    return request(options);
  }

  // 获取车辆保险excel模板
  getInsuranceExcelTemplate() {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/upload/get_download_url',
      body: { type: 3 },
    };
    return request(options);
  }
}

export default new InsuranceManage();
