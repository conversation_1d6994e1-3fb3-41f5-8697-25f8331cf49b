import { Table } from 'antd';
import React, { useEffect, useState } from 'react';
import { DeviceInfoApi } from '@/fetch/business';
import { pageSizeOptions } from '@/utils/constant';
import { HttpStatusCode } from '@/fetch/core/constant';
import { request } from '@/fetch/core';

const CustomTable = ({
  moduleName,
  searchFormValue,
}: {
  moduleName: 'vehicle' | 'cardNo';
  searchFormValue: any;
}) => {
  const fetchApi = new DeviceInfoApi();
  const [loading, setLoading] = useState(false);
  const [tableData, setTableData] = useState<any>({
    list: [],
    page: 1,
    size: 10,
    totalNumber: 0,
    totalPage: 0,
  });
  const [searchCondition, setSearchCondition] = useState({
    searchForm: {
      uploadType: moduleName === 'vehicle' ? 1 : 2,
      startModifyTime: searchFormValue.startTime,
      endModifyTime: searchFormValue.endTime,
    },
    current: 1,
    pageSize: 10,
  });

  const loadTableData = async () => {
    setLoading(true);
    const searchData = {
      ...searchCondition,
      pageNum: searchCondition.current,
    };
    const response: any = await fetchApi.getUploadedInfo(searchData);
    if (response && response.code === HttpStatusCode.Success) {
      setTableData({
        ...tableData,
        totalNumber: response.data.total,
        list: response.data.list,
        totalPage: response.data.pages,
      });
    }
    setLoading(false);
  };

  const handleTableChange = (
    paginationData: any,
    filters: any,
    sorter: any,
    extra: any,
  ) => {
    if (extra.action === 'paginate') {
      const { current, pageSize } = paginationData;
      setSearchCondition({
        ...searchCondition,
        current,
        pageSize,
      });
    }
  };

  useEffect(() => {
    setSearchCondition({
      searchForm: {
        ...searchCondition.searchForm,
        startModifyTime: searchFormValue.startTime,
        endModifyTime: searchFormValue.endTime,
      },
      current: 1,
      pageSize: 10,
    });
  }, [searchFormValue]);

  useEffect(() => {
    loadTableData();
  }, [searchCondition]);

  const getDownloadUrl = async (id: any, success: number) => {
    try {
      const result = await request({
        method: 'POST',
        path: '/k2/management/upload/upload_result_down',
        body: {
          id,
          success,
        },
      });

      if (
        result.code === HttpStatusCode.Success &&
        result.data &&
        result.data.url
      ) {
        // 将 http URL 替换为 https
        const secureUrl = result.data.url.replace('http:', 'https:');
        window.open(secureUrl, '_self');
      }
    } catch (error) {
      console.error('获取下载URL失败', error);
    }
  };

  return (
    <Table
      rowKey={(record: any) => record.id}
      columns={[
        {
          title: '导入表格',
          dataIndex: 'name',
          align: 'center',
          ellipsis: true,
          width: 220,
        },
        {
          title: '结果',
          dataIndex: 'resultDescription',
          align: 'center',
          ellipsis: true,
          width: 200,
        },
        {
          title: '操作人',
          dataIndex: 'modifyUser',
          align: 'center',
          ellipsis: true,
          width: 160,
        },
        {
          title: '操作时间',
          dataIndex: 'modifyTime',
          align: 'center',
          ellipsis: true,
          width: 180,
        },
        {
          title: '操作',
          dataIndex: '',
          align: 'center',
          ellipsis: true,
          width: 200,
          // eslint-disable-next-line react/display-name
          render: (params: any) => {
            const { enable } = params;
            const downloadSuccColor =
              enable === 1 && params.successCount > 0 ? '#0000ff' : '#808080';
            const downloadFailedColor =
              enable === 1 && params.failCount > 0 ? '#0000ff' : '#808080';

            // 处理成功列表下载
            const handleSuccessDownload = () => {
              if (params.successCount > 0 && enable === 1) {
                getDownloadUrl(params.id, 1);
              }
            };

            // 处理错误列表下载
            const handleFailedDownload = () => {
              if (params.failCount > 0 && enable === 1) {
                getDownloadUrl(params.id, 0);
              }
            };

            return (
              <div style={{ display: 'flex', justifyContent: 'space-around' }}>
                <a
                  onClick={handleSuccessDownload}
                  style={{
                    color: downloadSuccColor,
                    cursor:
                      enable === 1 && params.successCount > 0
                        ? 'pointer'
                        : 'default',
                  }}
                >
                  下载成功列表
                </a>
                <a
                  onClick={handleFailedDownload}
                  style={{
                    color: downloadFailedColor,
                    cursor:
                      enable === 1 && params.failCount > 0
                        ? 'pointer'
                        : 'default',
                  }}
                >
                  下载错误列表
                </a>
              </div>
            );
          },
        },
      ]}
      scroll={{
        x: '60vw',
        y: '200px',
      }}
      dataSource={tableData.list}
      loading={loading}
      bordered
      pagination={{
        position: ['bottomCenter'],
        total: tableData.totalNumber,
        pageSize: searchCondition.pageSize,
        current: searchCondition.current,
        showQuickJumper: true,
        showSizeChanger: true, // 显示可改变每页数量
        pageSizeOptions: pageSizeOptions, // 每页数量选项
        showTotal: (total) => `共 ${tableData.totalPage}页,${total} 条记录`, // 显示总数
      }}
      onChange={handleTableChange}
    />
  );
};

export default React.memo(CustomTable);
