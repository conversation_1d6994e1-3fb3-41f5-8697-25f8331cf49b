import { YESNO } from '@/utils/enum';
import { request } from '../core';
import { PageType } from '@/utils/enum';

export class RoleManageApi {
  // 分页查询数据列表
  fetchTableList({
    pageNum,
    pageSize,
    searchForm,
  }: {
    pageNum: number;
    pageSize: number;
    searchForm: Object;
  }) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/role/permission_get_role_page_list',
      body: { ...searchForm, pageNum, pageSize },
    };
    return request(options);
  }
  // 获取详情
  fetchDetail(roleNumber: number | string) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/role/permission_get_role_detail',
      body: { roleNumber },
    };
    return request(options);
  }
  //  新增编辑
  submitEditInfo({
    type,
    requestBody,
  }: {
    type: PageType.EDIT | PageType.ADD;
    requestBody: Object;
  }) {
    const options: RequestOptions = {
      method: 'POST',
      path:
        type === PageType.EDIT
          ? '/k2/management/role/permission_edit_role'
          : '/k2/management/role/permission_add_role',
      body: requestBody,
    };
    return request(options);
  }
  // 删除角色
  delRole(roleNumber: number | string) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/role/permission_delete_role',
      body: { roleNumber },
    };
    return request(options);
  }
  // 关联资源
  bindResource(roleNumber: number | string, resourceNumberList: any[]) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/role/permission_bind_resource',
      body: {
        roleNumber,
        resourceNumberList,
      },
    };
    return request(options);
  }
  // 根据角色编号查询已绑定的资源
  getResource(roleNumber: number | string) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/role/permission_get_resource_of_role',
      body: {
        roleNumber,
      },
    };
    return request(options);
  }
}
