import { request } from '@/fetch/core';

class XMapManageFetch {
  public async fetchXMapVersionData({
    mapId,
    currentPage,
    pageSize,
  }: {
    currentPage: number;
    pageSize: number;
    mapId?: number | null;
  }) {
    const requestOptions: RequestOptions = {
      path: '/map/web/metadata/listMapVersion',
      method: 'POST',
      body: mapId
        ? [
            { mapId: mapId },
            {
              pageNum: currentPage,
              pageSize: pageSize,
            },
          ]
        : [{}, { pageNum: currentPage, pageSize: pageSize }],
    };
    return request(requestOptions);
  }

  public async fetchXMapInfoData({
    mapId,
    currentPage,
    pageSize,
  }: {
    currentPage: number;
    pageSize: number;
    mapId?: number | null;
  }) {
    const requestOptions: RequestOptions = {
      path: '/map/web/metadata/listMapInfo',
      method: 'POST',
      body: mapId
        ? [
            { id: mapId },
            {
              pageNum: currentPage,
              pageSize: pageSize,
            },
          ]
        : [{}, { pageNum: currentPage, pageSize: pageSize }],
    };
    return request(requestOptions);
  }
}

export default XMapManageFetch;
