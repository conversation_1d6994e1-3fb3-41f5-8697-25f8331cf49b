import ModulePart from '@/components/ModulePart';
import {
  Button,
  Cascader,
  Col,
  Form,
  FormInstance,
  Input,
  Row,
  Select,
  message,
} from 'antd';
import React, { useEffect, useState } from 'react';
import ChooseLocation from '../../../ChooseLocation';
import { CommonApi, StationFetchApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';
import { CommonSelectOption } from '@/types';
const commonFetch = new CommonApi();
const stationFetch = new StationFetchApi();
interface StationBasicInfoProps {
  dropDownMap: {
    stationUseCase: CommonSelectOption;
    productTypeList: CommonSelectOption;
    stationTypeList: CommonSelectOption;
    companyList: CommonSelectOption;
    userList: CommonSelectOption;
  };
  setLocationInfo: React.Dispatch<any>;
  locationInfo: any;
  form: FormInstance<any>;
  disabled?: boolean;
  setShowBusinessInfo?: any;
}
const StationBasicInfo = (props: StationBasicInfoProps) => {
  const {
    dropDownMap,
    locationInfo,
    setLocationInfo,
    form,
    disabled,
    setShowBusinessInfo,
  } = props;
  const [showChooseLocation, setShowChooseLocation] = useState<Boolean>(false);
  const [stateCityList, setStateCityList] = useState();
  const [validStationName, setValidStationName] = useState<{
    validateStatus: 'success' | 'warning' | 'error' | 'validating' | '';
  }>({
    validateStatus: '',
  });
  const getDepartmentList = async (type: string) => {
    const res = await commonFetch.getCityDepartment();

    if (res.code === HttpStatusCode.Success) {
      setStateCityList(res.data);
    }
  };
  useEffect(() => {
    if (locationInfo) {
      if (locationInfo.initPosition) {
        form.setFieldsValue({
          address: locationInfo.address,
          coordinate:
            locationInfo.initPosition.lon + ' ' + locationInfo.initPosition.lat,
        });
      }
    }
    getDepartmentList('city');
  }, [locationInfo]);
  return (
    <>
      <ModulePart title="基础信息">
        <Row>
          <Col xxl={7} xl={7} lg={24} md={24}>
            <Form.Item
              name="name"
              label="站点名称"
              validateStatus={validStationName.validateStatus}
              hasFeedback
              rules={[
                { required: true, message: '请输入站点名称' },
                ({ getFieldValue }) => ({
                  async validator(_, value) {
                    const res = await stationFetch.checkDuplicateStationName({
                      name: value,
                    });
                    if (res.code != HttpStatusCode.Success) {
                      setValidStationName({
                        validateStatus: 'error',
                      });
                      return Promise.reject(new Error(res.message));
                    }
                    setValidStationName({
                      validateStatus: 'success',
                    });
                    return Promise.resolve();
                  },
                }),
              ]}
            >
              <Input placeholder="请输入站点名称" />
            </Form.Item>
          </Col>
          <Col xxl={7} xl={7} lg={24} md={24}>
            <Form.Item
              name="productType"
              label="站点类型"
              rules={[{ required: true, message: '请选择站点类型' }]}
            >
              <Select
                disabled={typeof disabled === 'undefined' ? false : true}
                options={dropDownMap.productTypeList as any}
                placeholder="请选择站点类型"
                onChange={(value) => {
                  if (value === 'robot') {
                    setShowBusinessInfo && setShowBusinessInfo(false);
                  } else {
                    setShowBusinessInfo && setShowBusinessInfo(true);
                  }
                }}
              />
            </Form.Item>
          </Col>
          <Col xxl={7} xl={7} lg={24} md={24}>
            <Form.Item
              name="type"
              label="运营方类型"
              rules={[{ required: true, message: '请选择站点运营方' }]}
            >
              <Select
                options={dropDownMap.stationTypeList as any}
                placeholder="请选择站点运营方"
              />
            </Form.Item>
          </Col>

          <Col xxl={7} xl={7} lg={24} md={24}>
            <Form.Item
              name="useCase"
              label="站点用途"
              rules={[{ required: true, message: '请选择站点用途' }]}
            >
              <Select
                placeholder="请选择站点用途"
                options={dropDownMap.stationUseCase as any}
                filterOption={(input: any, option: any) => {
                  const label: any = option?.label || '';
                  return (
                    label
                      .toString()
                      .toLowerCase()
                      .indexOf(input.toLowerCase()) >= 0
                  );
                }}
                allowClear
                showSearch={true}
              />
            </Form.Item>
          </Col>
          <Col xxl={7} xl={7} lg={24} md={24}>
            <Form.Item
              name="cityInfo"
              label="省份城市"
              rules={[{ required: true, message: '省份和城市不能为空' }]}
            >
              <Cascader
                fieldNames={{
                  label: 'name',
                  value: 'id',
                  children: 'children',
                }}
                options={stateCityList}
                placeholder={'请选择省份和城市'}
                showSearch={true}
                allowClear={true}
                getPopupContainer={(triggerNode) => triggerNode.parentElement}
                showCheckedStrategy={Cascader.SHOW_CHILD}
                onChange={(values, selectOptions) => {
                  selectOptions &&
                    setLocationInfo({
                      ...locationInfo,
                      cityId: selectOptions[1].id,
                      cityName: selectOptions[1].name,
                    });
                }}
              />
            </Form.Item>
          </Col>
          <Col xxl={7} xl={7} lg={24} md={24}>
            <Form.Item
              label="详细地址"
              name="address"
              rules={[{ required: true, message: '请输入详细地址' }]}
            >
              <Input
                placeholder="请输入详细地址"
                maxLength={40}
                onBlur={(e: any) => {
                  form.setFieldsValue({
                    address: e.target.value,
                  });
                  setLocationInfo({
                    ...locationInfo,
                    address: e.target.value,
                  });
                }}
              />
            </Form.Item>
          </Col>
          <Col xxl={10} xl={24} lg={24} md={24}>
            <Form.Item
              name="coordinate"
              label=" 经纬度"
              rules={[{ required: true, message: '请在地图上选点' }]}
            >
              <Input disabled placeholder="请在地图上选点" />
            </Form.Item>
          </Col>
          <Button
            className="choose-location"
            type="primary"
            onClick={() => {
              if (!locationInfo.cityId) {
                message.error('请先选择城市！');
                setShowChooseLocation(false);
              } else {
                setShowChooseLocation(true);
              }
            }}
          >
            地图选点
          </Button>
        </Row>
      </ModulePart>
      <ModulePart
        title="负责人信息"
        otherContentStyle={{ paddingBottom: '0px' }}
      >
        <Row>
          <Col xxl={8} xl={8} lg={12} md={24}>
            <Form.Item
              name={'personName'}
              label={'负责人姓名'}
              rules={[{ required: true, message: '请选择负责人姓名' }]}
            >
              <Select
                placeholder={'请选择负责人姓名'}
                options={dropDownMap.userList as any}
                filterOption={(input: any, option: any) => {
                  const label: any = option?.label || '';
                  return (
                    label
                      .toString()
                      .toLowerCase()
                      .indexOf(input.toLowerCase()) >= 0
                  );
                }}
                labelInValue
                allowClear
                showSearch={true}
                onChange={(value, options) => {
                  value &&
                    form.setFieldsValue({
                      contact: value.value.split('/')?.[1],
                    });
                }}
              />
            </Form.Item>
          </Col>
          <Col xxl={8} xl={8} lg={12} md={24}>
            <Form.Item
              name={'contact'}
              label={'负责人电话'}
              rules={[{ required: true, message: '请输入负责人电话' }]}
            >
              <Input placeholder={'请输入负责人电话'} allowClear />
            </Form.Item>
          </Col>
        </Row>
      </ModulePart>
      {showChooseLocation && (
        <ChooseLocation
          initPosition={locationInfo.initPosition}
          address={locationInfo.address}
          city={locationInfo.cityName}
          show={showChooseLocation}
          onClose={() => setShowChooseLocation(false)}
          onOk={(value: any) => {
            form.setFieldsValue({
              coordinate: value.position.lon + ' ' + value.position.lat,
            });
            message.success('地址选择成功！');
            setShowChooseLocation(false);
            setLocationInfo({
              ...locationInfo,
              address: value.address || locationInfo.address,
              initPosition: value.position,
            });
          }}
        />
      )}
    </>
  );
};
export default React.memo(StationBasicInfo);
