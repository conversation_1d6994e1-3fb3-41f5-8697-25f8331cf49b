import React, { useState, useEffect } from 'react';
import { CommonTable } from '@jd/x-coreui';
import { vehicleDemandManageApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';
import { message } from 'antd';
import { modifyLogTableColumns } from '../../utils/constant';

interface UpdateLogModalProps {
  requirementId: number;
}

const UpdateLogModal: React.FC<UpdateLogModalProps> = ({ requirementId }) => {
  const [loading, setLoading] = useState(false);
  const [tableData, setTableData] = useState<any>({
    list: [],
    total: 0,
    pages: 0,
    pageNum: 1,
    pageSize: 10,
  });

  const loadUpdateLog = async (pageNum: number = 1, pageSize: number = 10) => {
    if (!requirementId) {
      message.error('需求编号不能为空');
      return;
    }
    setLoading(true);
    try {
      const res = await vehicleDemandManageApi.getUpdateLog({
        requirementId,
        pageNum,
        pageSize,
      });
      if (res && res.code === HttpStatusCode.Success && res.data) {
        setTableData({
          list: res.data.list || [],
          total: res.data.total || 0,
          pages: res.data.pages || 0,
          pageNum: res.data.pageNum || 1,
          pageSize: res.data.pageSize || 10,
        });
      } else {
        message.error(res.message || '获取修改记录失败');
      }
    } catch (error) {
      console.error('获取修改记录失败:', error);
      message.error('获取修改记录失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadUpdateLog();
  }, [requirementId]);

  const handlePageChange = (params: any) => {
    loadUpdateLog(params.pageNum, params.pageSize);
  };

  return (
    <div className="update-log-modal">
      <CommonTable
        tableListData={{
          list: tableData.list,
          totalNumber: tableData.total,
          totalPage:
            tableData.pages || Math.ceil(tableData.total / tableData.pageSize),
        }}
        columns={modifyLogTableColumns}
        loading={loading}
        rowKey="id"
        searchCondition={{
          pageNum: tableData.pageNum,
          pageSize: tableData.pageSize,
        }}
        onPageChange={handlePageChange}
      />
    </div>
  );
};

export default UpdateLogModal;
