import React from 'react';
import { RootState } from '@/redux/store';
import { useSelector } from 'react-redux';
import { passportLogout } from '@/utils/authHelper';
const Header = () => {
  const { userName } = useSelector((state: RootState) => state.common);
  return (
    <>
      <div className="main-head-content">
        <div>无人车基础数据管理平台</div>
        <div className="basic-user-content">
          <img
            src={require('@/assets/image/common/userIcon.png')}
            className="avatar"
          ></img>
          <div className="username">{userName}</div>
          <div className="log-out-btn" onClick={passportLogout}>
            退出
          </div>
        </div>
      </div>
    </>
  );
};
export default React.memo(Header);
