.order-confirm-modal {
  width: 100%;
  .modal-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 24px;
    text-align: center;

    .warning-icon {
      font-size: 32px;
      color: #fa8c16;
      margin-bottom: 12px;
    }

    .header-title {
      font-size: 16px;
      font-weight: 500;
      color: #262626;
      margin-bottom: 8px;
    }

    .header-subtitle {
      font-size: 14px;
      color: #595959;
    }
  }

  > .ant-form-inline {
    display: block;
    flex-wrap: nowrap;
  }

  .order-list {
    max-height: 400px;
    overflow-y: auto;

    .order-card {
      padding: 16px;

      .order-card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        background-color: #f1f5ff;
        border-radius: 6px;
        padding: 8px;

        .header-left {
          display: flex;
          align-items: center;
          .check-icon {
            color: #52c41a;
            font-size: 16px;
            margin-right: 8px;
          }
          .selected-text {
            font-size: 14px;
            font-weight: 500;
            color: #262626;
          }
        }
        .header-right {
          display: flex;
          align-items: center;
          .info-item {
            font-size: 14px;
            color: #595959;
          }
          .title-divider {
            border-color: #d9d9d9;
            margin: 0 12px;
          }
        }
      }

      .delivery-month {
        margin-bottom: 16px;

        .ant-form-item {
          margin-bottom: 0;
        }

        .ant-form-item-label > label {
          color: #8c8c8c;
          font-weight: 500;
        }
      }

      .order-info {
        .info-row {
          display: flex;
          margin-bottom: 8px;

          &:last-child {
            margin-bottom: 0;
          }

          .info-col {
            flex: 1;
            display: flex;

            .label {
              font-size: 14px;
              color: #8c8c8c;
              min-width: 80px;
            }

            .value {
              font-size: 14px;
              color: #262626;
              flex: 1;
              word-break: break-all;
            }
          }
        }
      }
      .card-bottom-divider {
        width: 100%;
        height: 1px;
        background-color: #d9d9d9;
        margin-top: 24px;
        &.last-card {
          display: none;
        }
      }
    }
  }
}
