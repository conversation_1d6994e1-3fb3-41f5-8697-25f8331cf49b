.vehicle-demand-detail {
  padding: 20px 0;

  .main-content {
    display: flex;
    gap: 20px;
    margin-top: 15px;

    .header-title-container {
      display: flex;
      align-items: center;
      gap: 8px;
      .header-icon {
        font-size: 18px;
        color: #1890ff;
      }
      .header-title {
        font-size: 16px;
        font-weight: 600;
        color: #262626;
      }
    }
    .panel-header {
      padding: 20px;
      border-bottom: 1px solid #f0f0f0;
      display: flex;
      align-items: center;
      &.right-header {
        justify-content: space-between;
      }
      .action-container {
        display: flex;
        gap: 8px;
      }
    }

    .left-panel {
      flex: 0 0 350px;
      background: white;
      overflow-x: hidden;
      border-radius: 8px;
      display: flex;
      flex-direction: column;

      .vehicle-model-list {
        padding: 20px;
        overflow-y: auto;
        width: 100%;
        box-sizing: border-box;
        overflow-x: hidden;
      }
    }

    .right-panel {
      flex: 1;
      background: white;
      border-radius: 8px;
      display: flex;
      flex-direction: column;

      .form-container {
        flex: 1;
        padding: 20px;
        overflow-y: auto;

        .divider {
          width: 100%;
          height: 2px;
          background-color: #f0f0f0;
          margin: 16px 0 24px 0;
        }

        .ant-form-item {
          margin-bottom: 20px;
        }

        .ant-form-item-label > label {
          font-weight: 500;
          color: #262626;
        }

        .ant-divider {
          margin: 24px 0;
          border-color: #e8e8e8;
        }
      }
    }
  }

  .bottom-actions {
    padding: 20px;
    border-radius: 8px;
    padding-bottom: 15px;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}
