import { Form, Radio, message, Row, Col, Select, Input } from 'antd';
import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { CommonEdit, HardwareTemplate } from '@/components';
import { VehicleTypeApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';
import { formatLocation, formatOptions } from '@/utils/utils';
import { ProductType, YESNO } from '@/utils/enum';
import { dropDownListKey, dropDownKey, SUPPLIER } from '@/utils/constant';
import { VehicleTypeTitle, PageType } from '@/utils/EditTitle';
import { useEditPageData, useCommonDropDown } from '@/utils/hooks';
import SensorSchemeInfo from './component/SensorSchemeInfo';


const VehicleTypeEdit = () => {
  const fetchApi = new VehicleTypeApi();
  const navigator = useNavigate();
  const { id, type, productType } = formatLocation(window.location.search);
  const [form] = Form.useForm();
  const [selectedProductType, setSelectedProductType] = useState(productType);
  let dropdownData = useCommonDropDown([
    dropDownKey.PRODUCT_TYPE,
    dropDownKey.ENABLE,
    dropDownKey.SENSOR_SCHEME_NAME,
    dropDownKey.PRODUCT_MANUFACTORY,
    dropDownKey.SUPPLIER,
  ]);
  const [detailData, setDetailData] = useState<any>({ manufactoryId: null });
  const [productTypeList, setProductTypeList] = useState<any[]>([]);
  const getDetail = async () => {
    const res = await fetchApi.fetchDetail(id);
    if (res.code === HttpStatusCode.Success) {
      setDetailData(res.data);
      const formateData = {
        manufactoryId: res.data.manufactoryId
          ? {
              value: res.data.manufactoryId,
              label: res.data.manufactoryName,
            }
          : null,
      };
      form.setFieldsValue({
        ...res.data,
        ...formateData,
      });
    } else {
      message.error(res.message);
      navigator(-1);
    }
  };

  useEffect(() => {
    setProductTypeList(
      formatOptions(dropdownData[dropDownListKey.PRODUCT_TYPE]),
    );
  }, [dropdownData]);
  useEffect(() => {
    if (id) {
      getDetail();
    } else {
      form.setFieldsValue({
        productType: ProductType.VEHICLE,
        supplier: SUPPLIER.JD,
      });
      setSelectedProductType(ProductType.VEHICLE);
    }
  }, [id]);
  const onSubmitClick = async () => {
    const formValue = await form.validateFields();
    const hardwareModelList: any = [];
    Object.keys(formValue).forEach((key) => {
      if (key.includes('hardwareModel')) {
        hardwareModelList.push(formValue[key].value);
      }
    });
    const res = await fetchApi.submitInfo({
      type,
      requestBody: {
        id,
        name: formValue.name,
        productType: formValue.productType,
        enable: formValue.enable,
        sensorSchemeId: formValue.sensorSchemeId?.value,
        manufactoryId: formValue.manufactoryId?.value,
        hardwareModelList:
          hardwareModelList.length > 0 ? hardwareModelList : [],
        supplier: formValue?.supplier ?? null,
        // pictureList: formValue.pictureList,
      },
    });
    if (res.code === HttpStatusCode.Success) {
      message.success(res.message);
      navigator('/app/vehicleType');
    } else {
      message.error(res.message);
    }
  };

  return (
    <CommonEdit
      title={VehicleTypeTitle[type]}
      breadCrumbConfig={[
        {
          title: '车型信息管理',
          route: '',
        },
        {
          title: VehicleTypeTitle[type],
          route: '',
        },
      ]}
      onSubmitClick={onSubmitClick}
      onCancleClick={() => navigator('/app/vehicleType')}
      hideSubmit={type === PageType.READONLY}
      cancelTitle={type === PageType.READONLY ? '返回' : '取消'}
    >
      <Form form={form} labelCol={{ span: 5 }} wrapperCol={{ span: 18 }}>
        <Row>
          <Col span={21}>
            <Form.Item
              name={'productType'}
              label={'所属产品'}
              rules={[{ required: true, message: '请选择所属产品' }]}
            >
              <Radio.Group
                disabled={type !== PageType.ADD}
                options={productTypeList}
                onChange={(e) => {
                  setSelectedProductType(e.target.value);
                }}
              />
            </Form.Item>
            {form.getFieldsValue().productType === ProductType.VEHICLE ? (
              <Form.Item
                label="供应商"
                name="supplier"
                rules={[{ required: true, message: '请选择供应商' }]}
              >
                <Radio.Group
                  disabled={type !== PageType.ADD}
                  options={formatOptions(
                    dropdownData[dropDownListKey.SUPPLIER],
                  )}
                  onChange={(e) => {
                    const value = e.target.value;
                    if (value == SUPPLIER.NEOLIX || value == SUPPLIER.RINO) {
                      setProductTypeList([
                        {
                          value: ProductType.VEHICLE,
                          label: '无人车',
                        },
                      ]);
                    } else {
                      setProductTypeList(
                        formatOptions(
                          dropdownData[dropDownListKey.PRODUCT_TYPE],
                        ),
                      );
                    }
                  }}
                />
              </Form.Item>
            ) : null}
            <Form.Item
              label="车型名称"
              name="name"
              rules={[
                { required: true, message: '请输入厂商+系列+套件版本+序号' },
              ]}
            >
              <Input
                disabled={type === PageType.READONLY}
                maxLength={50}
                placeholder="请输入厂商+系列+套件版本+序号"
              />
            </Form.Item>
            <Form.Item label="车型id" name="id">
              <Input maxLength={50} placeholder="系统生成" disabled />
            </Form.Item>
            <HardwareTemplate
              type="device_type"
              formRef={form}
              disabled={type === PageType.READONLY}
              productType={selectedProductType}
              initialVal={detailData?.hardwareModelList ?? []}
            />
            <SensorSchemeInfo
              disabled={type === PageType.READONLY}
              options={formatOptions(
                dropdownData[dropDownListKey.SENSOR_SCHEME_NAME],
              )}
              form={form}
              initScheme={{
                value: detailData.sensorSchemeId,
                label: detailData.sensorSchemeName,
              }}
            />
            <Form.Item
              label="车辆组装厂商"
              name="manufactoryId"
              rules={[{ required: true, message: '请选择车辆组装厂商' }]}
            >
              <Select
                options={formatOptions(
                  dropdownData[dropDownListKey.PRODUCT_MANUFACTORY],
                )}
                labelInValue
                disabled={type === PageType.READONLY}
                placeholder="请选择厂商名称"
                showSearch
                filterOption={(input, option) => {
                  const label: any = option?.label || '';
                  return label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                }}
              />
            </Form.Item>

            <Form.Item
              name={'enable'}
              label={'车型状态'}
              rules={[{ required: true, message: '请选择车型状态' }]}
              initialValue={1}
            >
              <Radio.Group
                disabled={type === PageType.READONLY}
                options={formatOptions(dropdownData[dropDownListKey.ENABLE])}
              />
            </Form.Item>
            <Form.Item label=" " colon={false}>
              <div style={{ color: '#808080' }}>
                <p>状态说明：</p>
                <p>
                  1、只有“有效”的车型，才能被引用成为一个车型选择项，“无效”的车型，不能被引用；
                </p>
                <p>
                  2、车型状态从“有效”改为“无效”，历史有被引用，历史数据不受影响，再修改，该无效车型不在选择项范围内。
                </p>
              </div>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </CommonEdit>
  );
};

export default React.memo(VehicleTypeEdit);
