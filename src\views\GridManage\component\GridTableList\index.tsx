/* eslint-disable react/display-name */
/* eslint-disable no-unused-vars */
/* eslint-disable no-invalid-this */
/* eslint-disable require-jsdoc */
import { Form, message, Modal, Table, Popconfirm, Space } from 'antd';
import {
  SortableContainer,
  SortableElement,
  SortableHandle,
} from 'react-sortable-hoc';
import { arrayMoveImmutable } from 'array-move';
import { MenuOutlined } from '@ant-design/icons';
import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { CustomButton } from '@/components';
import { gridTableColums, robotGridTableColums } from '../dataProvider';
import GridEditModal from '../EditModal';
import { boxGridSelector } from '@/redux/reducer/boxGrid';
import { sendGlobalEvent } from '@/utils/emit';
import { GlobalEventName } from '@/utils/constant';
import './index.scss';
import { PageType, ProductType } from '@/utils/enum';
const DragHandle = SortableHandle(() => (
  <MenuOutlined style={{ cursor: 'grab', color: '#999' }} />
));
const SortableItem = SortableElement((props: any) => <tr {...props} />);
const SortableList = SortableContainer((props: any) => <tbody {...props} />);
const GridTableList = ({
  initValues,
  onGridChanged,
  disabled,
  viewType,
  productionType,
}: {
  initValues: any;
  onGridChanged: Function;
  disabled?: boolean;
  viewType?: any;
  productionType: ProductType;
}) => {
  const [showEditModal, setShowEditModal] = useState<{
    show: boolean;
    editItem: any;
  }>({
    show: false,
    editItem: null,
  });

  const [showUpdataStatusModal, setShowUpdateStatusModal] = useState<{
    show: boolean;
    gridInfo: any;
    title: string;
    content: string;
  }>({
    show: false,
    gridInfo: null,
    title: '',
    content: '',
  });

  const [tableData, setTableData] = useState<any[]>([]);
  const [tableColumns, setTableColumns] = useState<any[]>([]);
  const gridPositionValue: any = useSelector(boxGridSelector).gridPositionValue;
  const gridSizeList = useSelector(boxGridSelector).gridSizeList;
  const [dataSource, setDataSource] = useState<any[]>([]);
  useEffect(() => {
    if (initValues) {
      const tempArr = initValues.map((item: any) => {
        const ele = { ...item };
        ele.sizeStr = `${ele.width} × ${ele.length} × ${ele.height}`;
        return ele;
      });
      setTableData(tempArr);
    }
  }, [initValues]);

  useEffect(() => {
    const columnsList =
      productionType === ProductType.VEHICLE
        ? gridTableColums
        : robotGridTableColums;
    if (viewType !== PageType.READONLY) {
      const columns = [...columnsList];
      if (viewType !== PageType.READONLY) {
        columns.unshift({
          title: '',
          dataIndex: 'sort',
          align: 'center',
          width: 40,
          className: 'drag-visible',
          render: () => <DragHandle />,
        });
      }
      setTableColumns([
        ...columns,
        {
          title: '操作',
          align: 'center',
          width: 240,
          render: (params: any, row: any, index: any) => {
            const status = params.enable === 1 ? true : false;
            return (
              <div>
                {' '}
                <Space size="middle">
                  <a
                    style={{ color: status ? '#D9001B' : '#31C2A6' }}
                    onClick={() => {
                      setShowUpdateStatusModal({
                        show: true,
                        gridInfo: params,
                        title: `是否确认${status ? '停用' : '启用'}`,
                        content: status
                          ? '格口停用后，该格口关联的所有业务数据将无法应用于业务运营'
                          : '格口启用后，该格口关联的所有业务数据将应用于业务运营',
                      });
                    }}
                  >
                    {status ? '停用' : '启用'}
                  </a>
                  {viewType === PageType.READONLY ? null : (
                    <>
                      <a
                        onClick={() => {
                          if (!validateAddGrid('edit')) return;
                          setShowEditModal({
                            show: true,
                            editItem: params,
                          });
                        }}
                      >
                        编辑
                      </a>
                      <a
                        onClick={() => {
                          if (!validateAddGrid('add')) return;
                          const copyItem = Object.assign({}, params);
                          copyItem.enable = 1;
                          setShowEditModal({
                            show: true,
                            editItem: {
                              ...copyItem,
                              id: null,
                              gridNo: tableData.length + 1,
                              lockNo: null,
                              boardNo: null,
                              isAdd: true,
                              palletList: copyItem.palletList?.map(
                                (e: any, index: number) => ({
                                  ...e,
                                  id: null,
                                }),
                              ),
                            },
                          });
                        }}
                      >
                        复制并新增
                      </a>
                      <Popconfirm
                        title="请确认是否删除该格口?"
                        onConfirm={() => {
                          deleteGridClick(index);
                        }}
                      >
                        <a style={{ color: '#D9001B' }}>删除</a>
                      </Popconfirm>
                    </>
                  )}
                </Space>
              </div>
            );
          },
        },
      ]);
    } else {
      setTableColumns(columnsList);
    }
  }, [tableData, gridPositionValue, gridSizeList]);

  useEffect(() => {
    const list = tableData.map((ele: any) => ({
      ...ele,
      pallet: ele.palletList?.map((p: any) => p.name).join(';') || '无',
    }));
    setDataSource(list);
  }, [tableData]);

  // 格口编辑结束
  const handelEditComplete = (value: any) => {
    if (value) {
      let editedList = tableData;
      if (showEditModal.editItem.isAdd) {
        editedList = [...tableData, value];
      } else {
        editedList = [
          ...tableData.map((ele: any) => {
            if (ele.gridNo === value.gridNo) {
              return value;
            }
            return ele;
          }),
        ];
      }
      setTableData(editedList);
      onGridChanged && onGridChanged(editedList);
    }
    setShowEditModal({
      show: false,
      editItem: null,
    });
  };

  // 格口状态变更
  const handelUpdateGridStatus = () => {
    const updatedItem = showUpdataStatusModal.gridInfo;
    updatedItem.enable = updatedItem.enable === 1 ? 0 : 1;
    const editedList = tableData?.map((ele: any) => {
      if (ele.gridNo === updatedItem.gridNo) {
        return updatedItem;
      }
      return ele;
    });
    setTableData(editedList);
    onGridChanged && onGridChanged(editedList);
  };

  const deleteGridClick = (idx: number) => {
    let tempArr = [...tableData];
    tempArr.splice(idx, 1);
    tempArr = tempArr.map((item: any, index: number) => {
      const ele = { ...item };
      ele.gridNo = index + 1;
      return ele;
    });
    setTableData([...tempArr]);
    onGridChanged && onGridChanged(tempArr);
  };

  const repeatSize = (type: string) => {
    for (let i = 0; i < gridSizeList.length; i++) {
      const item: any = gridSizeList[i];
      if (!item.width || !item.length || !item.height) {
        sendGlobalEvent(GlobalEventName.EVENT_VALID_CONFIG);
        return true;
      }
      for (let j = i + 1; j < gridSizeList.length; j++) {
        const element: any = gridSizeList[j];
        if (
          item.sizeStr === element.sizeStr &&
          element.width &&
          element.length &&
          element.height
        ) {
          message.warning(
            `格口规格宽高深(mm): ${item.width} × ${item.length} × ${
              item.height
            } 存在重复，请核实后再${type == 'add' ? '新增' : '编辑'}格口！`,
          );
          return true;
        }
      }
    }
    return false;
  };

  const validateAddGrid = (type: string) => {
    if (
      (!gridPositionValue.leftBoxColumnNum ||
        gridPositionValue.leftBoxColumnNum == 0) &&
      (!gridPositionValue.rightBoxColumnNum ||
        gridPositionValue.rightBoxColumnNum == 0) &&
      productionType === ProductType.VEHICLE
    ) {
      message.warning('请先输入货箱列数');
      return false;
    } else if (!gridSizeList.some((item: any) => item.sizeStr)) {
      message.warning('请先配置货箱格口规格');
      return false;
    } else if (repeatSize(type)) {
      return false;
    }
    return true;
  };

  const onSortEnd = (param: any) => {
    const { oldIndex, newIndex } = param;
    if (oldIndex !== newIndex) {
      let newData = arrayMoveImmutable(
        [...tableData],
        oldIndex,
        newIndex,
      ).filter((el: any) => !!el);
      newData = newData.map((item: any, index: number) => {
        const ele = { ...item };
        ele.gridNo = index + 1;
        return ele;
      });
      setTableData([...newData]);
      onGridChanged && onGridChanged(newData);
    }
  };

  const DraggableContainer = (props: any) => (
    <SortableList
      useDragHandle
      disableAutoscroll
      helperClass="row-dragging"
      onSortEnd={onSortEnd}
      {...props}
    />
  );

  const DraggableBodyRow = (param: any) => {
    const { className, style, ...restProps } = param;
    const index = tableData.findIndex(
      (x: any) => x.gridNo === restProps['data-row-key'],
    );
    return <SortableItem index={index} {...restProps} />;
  };

  return (
    <>
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
        }}
      >
        {viewType === PageType.READONLY ? null : (
          <CustomButton
            title="新增格口"
            otherCSSProperties={{ width: '90px', marginBottom: '20px' }}
            onSubmitClick={() => {
              if (!validateAddGrid('add')) return;
              setShowEditModal({
                show: true,
                editItem: { gridNo: tableData.length + 1, isAdd: true },
              });
            }}
          />
        )}
        <div>
          <Table
            rowKey={(reocrd) => reocrd.gridNo}
            bordered
            columns={tableColumns}
            dataSource={dataSource}
            pagination={false}
            components={{
              body: {
                wrapper: DraggableContainer,
                row: DraggableBodyRow,
              },
            }}
          />
        </div>
      </div>
      {showEditModal.show ? (
        <GridEditModal
          show={true}
          initValue={showEditModal.editItem}
          productionType={productionType}
          onSubmit={(value: any) => {
            handelEditComplete(value);
          }}
        />
      ) : null}
      {showUpdataStatusModal.show ? (
        <Modal
          visible={true}
          title={showUpdataStatusModal.title}
          maskClosable={false}
          onCancel={() => {
            setShowUpdateStatusModal({
              title: '',
              content: '',
              show: false,
              gridInfo: null,
            });
          }}
          onOk={() => {
            handelUpdateGridStatus();
            setShowUpdateStatusModal({
              title: '',
              content: '',
              show: false,
              gridInfo: null,
            });
          }}
        >
          <div>{showUpdataStatusModal.content}</div>
        </Modal>
      ) : null}
    </>
  );
};

export default React.memo(GridTableList);
