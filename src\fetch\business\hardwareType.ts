import { YESNO } from '@/utils/enum';
import { request } from '../core';
import { PageType } from '@/utils/enum';
export class HardwareTypeApi {
  // 分页查询硬件类型数据列表
  fetchTableList({
    pageNum,
    pageSize,
    searchForm,
  }: {
    pageNum: number;
    pageSize: number;
    searchForm: Object;
  }) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/hardware_type/hardware_type_get_page_list',
      body: { ...searchForm, pageNum, pageSize },
    };
    return request(options);
  }
  //  新增编辑硬件类型
  submitHardwareType({
    type,
    requestBody,
  }: {
    type: PageType.EDIT | PageType.ADD;
    requestBody: Object;
  }) {
    const options: RequestOptions = {
      method: 'POST',
      path:
        type === PageType.EDIT
          ? '/k2/management/hardware_type/hardware_type_edit'
          : '/k2/management/hardware_type/hardware_type_add',
      body: requestBody,
    };
    return request(options);
  }
  // 获取硬件类型详情
  fetchHardwearTypeDetail(id: number | string) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/hardware_type/hardware_type_get_detail',
      body: {
        id,
      },
    };
    return request(options);
  }
  // 启用停用硬件类型
  updateHardwearTypeStatus({ id, enable }: { id: number; enable: YESNO }) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/hardware_type/hardware_type_update_enable',
      body: {
        id,
        enable,
      },
    };
    return request(options);
  }

  // 是否可以删除硬件类型用途
  canDeleteUsage(id: number) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/hardware_type/hardware_type_usage_can_delete',
      body: {
        id,
      },
    };
    return request(options);
  }

  //   查询传感器数据下拉列表
  getSensorList() {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/hardware_type/hardware_type_sensor_get_select_list',
    };
    return request(options);
  }
  // 获取硬件类型下的所有硬件型号
  getHardwareModel(hardwareTypeId: number) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/hardware_type/get_hardware_model_of_type',
      body: {
        hardwareTypeId,
      },
    };
    return request(options);
  }
  // 查询硬件类型用途数据列表
  getUsageList({ hardwareTypeId }: { hardwareTypeId: number }) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/hardware_type/hardware_type_usage_get_select_list',
      body: {
        hardwareTypeId,
      },
    };
    return request(options);
  }
}
