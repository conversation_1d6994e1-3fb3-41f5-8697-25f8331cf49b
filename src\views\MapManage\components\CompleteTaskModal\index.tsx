import React, { forwardRef, useImperativeHandle, useRef } from 'react';
import { CommonForm } from '@jd/x-coreui';
import { completeTaskFormConfig } from '../../utils/constant';
import { mapManageApi } from '@/fetch/business';
import { isNumber } from '@/utils/utils';
import { HttpStatusCode } from '@/fetch/core/constant';
import { message } from 'antd';
import './index.scss';

interface CompleteTaskModalProps {
  taskId: number;
}

const CompleteTaskModal = forwardRef<any, CompleteTaskModalProps>(
  ({ taskId }, ref) => {
    const formRef = useRef<any>({});

    useImperativeHandle(ref, () => ({
      handleSubmit: async () => {
        if (!isNumber(Number(taskId))) {
          message.error('任务ID异常');
          return;
        }
        try {
          const values = await formRef.current?.validateFields();
          const formattedData = {
            ...values,
            completeTime: values.completeTime?.format('YYYY-MM-DD') || '',
          };
          handleCompleteTaskConfirm(formattedData);
        } catch (error) {
          console.error('表单验证失败:', error);
          message.error('请检查表单填写是否正确');
          throw error;
        }
      },
    }));

    const handleCompleteTaskConfirm = async (formData: any) => {
      try {
        const response = await mapManageApi.completeTask({
          taskId: Number(taskId),
          ...formData,
        });
        if (response.code === HttpStatusCode.Success) {
          message.success('完成任务成功');
        } else {
          message.error(response.message || '完成任务失败');
        }
      } catch (error) {
        console.error('完成任务失败:', error);
        message.error('完成任务失败');
      }
    };

    return (
      <div className="complete-task-modal">
        <div className="modal-content">
          <CommonForm
            formConfig={completeTaskFormConfig}
            layout="horizontal"
            formType="edit"
            colon={true}
            labelAlign="right"
            getFormInstance={(ref) => {
              formRef.current = ref;
            }}
          />
        </div>
      </div>
    );
  },
);

export default CompleteTaskModal;
