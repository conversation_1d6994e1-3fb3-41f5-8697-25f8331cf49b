.configure-vehicle {
  position: relative;
  height: calc(
    100% - 60px + 16px
  ); // 主页面的高度为100%，减去底部按钮高度60px，加上main的padding-bottom高度16px
  overflow-y: auto; // 覆盖main的overflow
  padding-right: 12px; // 滚动条左侧的空隙
  margin-right: -12px; // 去除main的空滚动条宽度

  .card {
    margin-bottom: 20px;
  }

  .info-content {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 10px;

    .highlight {
      color: #1890ff;
      font-weight: 600;
    }

    .info {
      font-size: 14px;
      color: #262626;
    }

    .info-divider {
      height: 20px;
      margin: 0 16px;
      border-left: 1px solid #d9d9d9;
    }
  }

  .vehicle-forms {
    .card-header {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 16px;

      .header-icon {
        font-size: 18px;
        color: #1890ff;
      }

      .header-title {
        font-size: 16px;
        font-weight: 600;
        color: #262626;
      }
    }

    .card-content {
      padding-top: 8px;
    }
  }

  .bottom-actions {
    position: fixed; // fixed固定在整个页面100vw的底部，横跨左侧菜单
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 60px;
    background: #fff;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    border-top: 1px solid #f0f0f0;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.06);
    z-index: 1; // 左侧菜单的main-menu-content的z-index为100，这里不能大于100
    button {
      margin-right: 12px;
    }
  }
}
