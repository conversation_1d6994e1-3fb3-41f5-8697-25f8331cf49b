import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import {
  FormConfig,
  CommonForm,
  CommonTable,
  sendGlobalEvent,
} from '@jd/x-coreui';
import { Button, Col, Input, InputNumber, message, Row } from 'antd';
import { showModal } from '@/components';
import { MaintainAbnormalDurationApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';
import {
  AddAbnormalDurationFormConfig,
  RecordMode,
  AbnormalTableColumns,
} from '../../utils/columns';
import './index.scss';

const AbnormalDurationForm = forwardRef(
  (
    {
      type,
      tableList,
      formVal,
    }: {
      type: 'add' | 'edit';
      tableList?: any[];
      formVal?: any;
    },
    ref,
  ) => {
    const fetchApi = new MaintainAbnormalDurationApi();
    const formRef = useRef<any>(null);
    const operateTime = useRef<any>({
      startTime: null,
      endTime: null,
    });
    const abnormalTime = useRef<any>({
      startTime: null,
      endTime: null,
    });
    const [formDefaultValue, setFormDefaultValue] = useState<any>({
      recordMode: RecordMode.VEHICLE,
      disabledCauseKey: null,
      addDisabledCause: 1,
    });
    const [list, setList] = useState<any[]>([]);
    const [update, setUpdate] = useState<number>(0);
    const [updateFormRef, setUpdateFormRef] = useState<boolean>(false);

    useEffect(() => {
      tableList && setList(tableList);
    }, [JSON.stringify(tableList)]);

    useEffect(() => {
      formVal &&
        setFormDefaultValue((prevState: any) => ({
          ...prevState,
          recordMode: null,
          disabledCauseKey: formVal?.disabledCauseKey,
        }));
    }, [JSON.stringify(formVal)]);

    useEffect(() => {
      if (type === 'add') {
        fetchApi.getOperateTime().then((res) => {
          if (res.code === HttpStatusCode.Success) {
            operateTime.current = res.data;
            const val = AddAbnormalDurationFormConfig.fields.filter(
              (v) => v.fieldName === 'abnormalTime',
            );
            const start = parseInt(
              operateTime.current?.startTime?.split(':')[0],
              10,
            );
            const end = parseInt(
              operateTime.current?.endTime?.split(':')[0],
              10,
            );

            sendGlobalEvent('FORCE_UPDATE_ONE_CONFIG', {
              name: 'abnormal-duration-form',
              fieldName: 'abnormalTime',
              config: {
                ...val[0],
                showTime: {
                  hideDisabledOptions: true,
                  disabledTime: (_, type) => {
                    return {
                      disabledHours: () => {
                        return Array.from({ length: 24 }, (_, i) => i).filter(
                          (v) => v < start || v > end,
                        );
                      },
                    };
                  },
                },
              },
            });
          }
        });
      }
    }, [type]);

    useImperativeHandle(
      ref,
      () => {
        return {
          formRef: formRef.current,
          list,
          fillNotOperateTime,
          checkInvalidVal,
        };
      },
      [list, updateFormRef],
    );

    const checkInvalidVal = () => {
      setUpdate(update + 1);
    };

    const fillNotOperateTime = () => {
      setList(
        list?.map((v) => ({
          ...v,
          notOperationDuration: v.disabledDuration,
        })),
      );
    };

    const formatFormConfig = (): FormConfig => {
      return {
        fields: AddAbnormalDurationFormConfig.fields.map((v) => {
          switch (v.fieldName) {
            case 'addDisabledCause':
              return {
                ...v,
                renderFunc: (data) => {
                  return (
                    <a
                      type="primary"
                      style={{ marginLeft: '10px' }}
                      onClick={() => createCauseModal()}
                    >
                      新建原因
                    </a>
                  );
                },
              };
            case 'abnormalTime':
            case 'recordMode':
              return {
                ...v,
                hidden: type === 'edit',
              };
            default:
              return v;
          }
        }),
        linkRules: AddAbnormalDurationFormConfig.linkRules,
      };
    };

    const formatColumns = (): any[] => {
      return AbnormalTableColumns.map((v) => {
        switch (v.dataIndex) {
          case 'notOperationDuration':
            return {
              ...v,
              render: (text: any, record: any, index: number) => {
                return (
                  <div
                    className={
                      record.notOperationDuration == undefined && update > 0
                        ? 'effectTime-input red'
                        : 'effectTime-input'
                    }
                  >
                    <InputNumber
                      placeholder="请输入数字"
                      value={record.notOperationDuration}
                      precision={2}
                      min={0}
                      style={{
                        border:
                          record.notOperationDuration == undefined && update > 0
                            ? '1px solid #ff4d4f'
                            : '',
                      }}
                      onChange={(inputVal) => {
                        setList(
                          list.map((v) =>
                            v.startTime === record.startTime
                              ? { ...v, notOperationDuration: inputVal }
                              : v,
                          ),
                        );
                      }}
                    />
                  </div>
                );
              },
            };
          default:
            return v;
        }
      });
    };

    const createCauseModal = () => {
      let inputVal;
      showModal({
        title: '创建原因',
        content: (
          <Input
            placeholder="请输入原因名称"
            allowClear
            onChange={(e) => (inputVal = e.target.value)}
            maxLength={30}
          />
        ),
        footer: {
          showOk: true,
          showCancel: true,
          okFunc: async (cb) => {
            if (typeof inputVal !== 'string' || inputVal.length <= 0) {
              message.warning('请输入原因名称');
              return;
            }
            const res = await fetchApi.createDisabledCause({ name: inputVal });
            if (res.code === HttpStatusCode.Success) {
              cb();
              setFormDefaultValue((prevState: any) => ({
                ...prevState,
                disabledCauseKey: res.data.key,
                addDisabledCause: prevState.addDisabledCause + 1,
              }));
            }
          },
          cancelFunc: (cb) => cb(),
        },
      });
    };

    function timestampToFormattedDate(timestamp) {
      const date = new Date(timestamp);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:00`;
    }

    const formatTableList = (t) => {
      const start = t[0].format('YYYY-MM-DD HH'); // 用户选择的异常时段开始时间
      const end = t[1].format('YYYY-MM-DD HH'); // 用户选择的异常时段结束时间
      const startDate = start.split(' ')[0];
      const endDate = end.split(' ')[0];
      const dayStartHour = operateTime.current?.startTime?.split(':')[0]; // 运营开始时间
      const dayEndHour = operateTime.current?.endTime?.split(':')[0]; // 运营结束时间
      let days: any[] = [];
      // 首日
      const firstDayStartTime = parseInt(start.split(' ')[1], 10);
      const firstDayEndTime =
        startDate === endDate
          ? Math.min(parseInt(dayEndHour, 10), parseInt(end.split(' ')[1], 10))
          : parseInt(dayEndHour, 10);

      days[0] = {
        startTime: timestampToFormattedDate(
          new Date(t[0]).setHours(firstDayStartTime, 0, 0),
        ),
        endTime: timestampToFormattedDate(
          new Date(t[0]).setHours(firstDayEndTime, 0, 0),
        ),
        disabledDuration: firstDayEndTime - firstDayStartTime,
      };
      // 中间的
      let cur = new Date(startDate);
      cur.setDate(cur.getDate() + 1);
      while (cur < new Date(endDate)) {
        const dayStart = cur.setHours(parseInt(dayStartHour, 10));
        const dayEnd = cur.setHours(parseInt(dayEndHour, 10));
        days.push({
          startTime: timestampToFormattedDate(dayStart),
          endTime: timestampToFormattedDate(dayEnd),
          disabledDuration:
            parseInt(dayEndHour, 10) - parseInt(dayStartHour, 10),
        });
        cur.setDate(cur.getDate() + 1);
      }
      // 最后一天
      if (startDate !== endDate) {
        const lastDayStartTime = parseInt(dayStartHour, 10);
        const lastDayEndTime = parseInt(end.split(' ')[1], 10);
        days.push({
          startTime: timestampToFormattedDate(cur.setHours(lastDayStartTime)),
          endTime: timestampToFormattedDate(cur.setHours(lastDayEndTime)),
          disabledDuration: lastDayEndTime - lastDayStartTime,
        });
      }
      setList(days);
    };

    return (
      <div className="abnormal-duration-form">
        <CommonForm
          name="abnormal-duration-form"
          formConfig={formatFormConfig()}
          defaultValue={{
            ...formDefaultValue,
            breakdown: formVal?.breakdown?.toString(),
          }}
          getFormInstance={(ref) => {
            setUpdateFormRef(true);
            formRef.current = ref;
          }}
          onValueChange={async (val) => {
            if (
              val.abnormalTime?.length === 2 &&
              (val.abnormalTime[0] !== abnormalTime.current.startTime ||
                val.abnormalTime[1] !== abnormalTime.current.endTime)
            ) {
              abnormalTime.current = {
                startTime: val.abnormalTime[0],
                endTime: val.abnormalTime[1],
              };
              formatTableList(val.abnormalTime);
            }
          }}
        />
        <Col span={23} className="table-container">
          <Row>
            <Col span={5}>
              {type === 'edit' ? (
                <div className="table-label">异常时段</div>
              ) : (
                ''
              )}
            </Col>
            <Col span={18}>
              <CommonTable
                columns={formatColumns()}
                notPage={true}
                rowKey="startTime"
                tableListData={{ list: list }}
              />
            </Col>
          </Row>
        </Col>
      </div>
    );
  },
);

export default React.memo(AbnormalDurationForm);
