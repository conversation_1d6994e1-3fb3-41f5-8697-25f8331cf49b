import React, { useState, useEffect } from 'react';
import { CommonTable, CommonForm, TableOperateBtn } from '@/components';
import { searchConfig, tableColumns } from './utils/columns';
import { useTableData } from '@/components/CommonTable/useTableData';
import { AppOperationApi } from '@/fetch/business';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '@/redux/store';
import { useNavigate } from 'react-router-dom';
import { Space, Modal, Form, Row, Col, message } from 'antd';
import { EyeOutlined } from '@ant-design/icons';
import ReactJson from 'react-json-view';
import dayjs, { Dayjs } from 'dayjs';

/**
 * JSON格式化展示
 * @param {string}param0
 * @return {any} value
 */
const ReactJsonView = ({ src }: { src: string }) => {
  return (
    <>
      {src && src != '-' ? (
        <ReactJson
          name={false}
          collapsed={true}
          displayObjectSize={false}
          displayDataTypes={false}
          src={JSON.parse(src)}
        />
      ) : (
        src
      )}
    </>
  );
};
const AppOperation = () => {
  const navigator = useNavigate();
  const dispatch = useDispatch();
  const [modalFormRef] = Form.useForm();
  const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
  const [editItem, setEditItem] = useState<any>({});
  const fetchApi = new AppOperationApi();
  const historySearchValues = useSelector(
    (state: RootState) => state.searchForm,
  );
  const initSearchCondition = {
    searchForm: {
      app: null,
      module: null,
      operationType: null,
      requestParam: null,
      uri: null,
      flag: null,
      time: null,
      queryTimeStart: null,
      queryTimeEnd: null,
    },
    pageNum: 1,
    pageSize: 10,
  };
  const [searchCondition, setSearchCondition] = useState<SearchCondition>(
    () => {
      return historySearchValues.searchValues
        ? historySearchValues.searchValues
        : initSearchCondition;
    },
  );
  const { tableData, loading } = useTableData(
    {
      ...searchCondition,
      searchForm: {
        ...searchCondition.searchForm,
        app: searchCondition.searchForm.app?.value,
        module: searchCondition.searchForm.module?.value,
        operationType: searchCondition.searchForm.operationType?.value,
        flag: searchCondition.searchForm.flag?.value,
      },
    },
    fetchApi.fetchTableList,
  );

  const formateColumns = () => {
    return tableColumns.map((col) => {
      switch (col.dataIndex) {
        case 'appNumber':
          return {
            ...col,
            render: (text: any, record: any, index: number) => {
              const { pageSize, pageNum } = searchCondition;
              return <div>{(pageNum - 1) * pageSize + index + 1}</div>;
            },
          };
        case 'operation':
          return {
            ...col,
            render: (text: any, record: any, index: number) => {
              return (
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  <Space>
                    <span
                      style={{ color: 'rgb(49, 194, 166)', cursor: 'pointer' }}
                    >
                      <EyeOutlined
                        onClick={() => {
                          moreInfoClick(record);
                        }}
                      />
                    </span>
                  </Space>
                </div>
              );
            },
          };
        default:
          return {
            ...col,
            render: (text: any) => `${text || '-'}`,
          };
      }
    });
  };

  const moreInfoClick = (item: any) => {
    setEditItem(item);
    modalFormRef.setFieldsValue({
      method: item.method,
      requestParam: item.requestParam,
      returnResult: item.returnResult,
      exceptionName: item.exceptionName,
      exceptionMessage: item.exceptionMessage,
    });
    setIsModalVisible(true);
  };

  const onSearchClick = (val) => {
    const incompatibleNum = [val.app, val.module, val.operationType].filter(
      (item) => item == null,
    ).length;
    if (0 < incompatibleNum && incompatibleNum < 3) {
      message.warning('必须同时选择应用服务、功能模块、操作类型才能继续查询');
      return;
    }
    let startTime: any;
    let endTime: any;
    if (val?.time && val?.time.length > 0) {
      const startMoment: Dayjs = val?.time[0];
      if (startMoment) {
        startTime = startMoment.format('YYYY-MM-DD HH:mm:ss');
      }
      const endMoment: Dayjs = val?.time[1];
      if (endMoment) {
        endTime = endMoment.format('YYYY-MM-DD HH:mm:ss');
      }
    }
    delete val.time;
    const data = {
      searchForm: { ...val, queryTimeStart: startTime, queryTimeEnd: endTime },
      pageNum: 1,
      pageSize: 10,
    };
    setSearchCondition(data);
  };

  const handleCancelClick = () => {
    setIsModalVisible(false);
  };
  const handleOkClick = () => {
    setIsModalVisible(false);
  };
  return (
    <>
      <CommonForm
        formConfig={searchConfig}
        defaultValue={searchCondition.searchForm}
        layout="inline"
        formType="search"
        colon={false}
        onSearchClick={onSearchClick}
        onResetClick={() => setSearchCondition({ ...initSearchCondition })}
      />
      <CommonTable
        tableListData={{
          list: tableData?.list ?? [],
          totalNumber: tableData?.total,
          totalPage: tableData?.pages,
        }}
        columns={formateColumns()}
        loading={loading}
        rowKey={'id'}
        searchCondition={searchCondition}
        onPageChange={(value: any) => setSearchCondition(value)}
      />
      <Modal
        title="请求信息"
        visible={isModalVisible}
        onCancel={handleCancelClick}
        onOk={handleOkClick}
        destroyOnClose
        width={700}
      >
        <Row gutter={20}>
          <Col span={4} className="t-a-r p-r-10">
            方法
          </Col>
          <Col span={20} className="light-font-color">
            {editItem.method}
          </Col>
        </Row>
        <Row gutter={20} className="m-t-20">
          <Col span={4} className="t-a-r p-r-10">
            请求URI
          </Col>
          <Col span={20} className="light-font-color">
            {editItem.uri}
          </Col>
        </Row>
        <Row gutter={20} className="m-t-20">
          <Col span={4} className="t-a-r p-r-10">
            请求IP
          </Col>
          <Col span={20} className="light-font-color">
            {editItem.ip}
          </Col>
        </Row>
        <Row gutter={20} className="m-t-20">
          <Col span={4} className="t-a-r p-r-10">
            请求参数
          </Col>
          <Col span={20} className="light-font-color">
            <ReactJsonView src={editItem.requestParam}></ReactJsonView>
          </Col>
        </Row>
        {editItem && editItem.flagName === '是' ? (
          <Row gutter={20} className="m-t-20">
            <Col span={4} className="t-a-r p-r-10">
              返回结果
            </Col>
            <Col span={20} className="light-font-color">
              <ReactJsonView src={editItem.returnResult}></ReactJsonView>{' '}
            </Col>
          </Row>
        ) : (
          <>
            <Row gutter={20} className="m-t-20">
              <Col span={4} className="t-a-r p-r-10">
                异常名称
              </Col>
              <Col span={20} className="light-font-color">
                {editItem.exceptionName}
              </Col>
            </Row>
            <Row gutter={20} className="m-t-20">
              <Col span={4} className="t-a-r p-r-10">
                异常信息
              </Col>
              <Col span={20} className="light-font-color">
                {editItem.exceptionMessage}
              </Col>
            </Row>
          </>
        )}
      </Modal>
    </>
  );
};

export default React.memo(AppOperation);
