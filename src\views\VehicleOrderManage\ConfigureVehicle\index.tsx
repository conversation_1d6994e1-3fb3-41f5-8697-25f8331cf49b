import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useNavigate, useSearchParams, useLocation } from 'react-router-dom';
import { Card, Divider, Button, message } from 'antd';
import { UnorderedListOutlined } from '@ant-design/icons';
import { CommonForm } from '@jd/x-coreui';
import { BreadCrumb } from '@/components';
import { vehicleOrderManageApi, commonApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';
import { useCommonDropDown } from '@/utils/hooks';
import { configureVehicleFormConfig } from '../utils/constant';
import './index.scss';

const ConfigureVehicle: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const location = useLocation();
  const orderId = searchParams.get('id');
  const orderCount = parseInt(searchParams.get('count') || '0');
  const vehicleModel = searchParams.get('vehicleModel');
  const [submitting, setSubmitting] = useState(false);
  const allFormRef = useRef<any[]>([]);
  const [vehicleTypeNames, setVehicleTypeNames] = useState<{
    [key: string]: string;
  }>({});
  const [formConfigs, setFormConfigs] = useState<any[]>(() =>
    Array.from({ length: orderCount }).map(() => configureVehicleFormConfig),
  );
  const [defaultValues, setDefaultValues] = useState<any[]>([]);
  const lastVehicleNameRef = useRef<string>('');

  const dropDownData = useCommonDropDown(['VEHICLE_TYPE']);

  const breadcrumbItems = [
    {
      title: '订单管理',
      route: '/app/vehicleOrderManage',
    },
    {
      title: '新增配车信息',
      route: '',
    },
  ];

  useEffect(() => {
    if (dropDownData?.deviceTypeBaseInfoList?.length > 0) {
      initializeFormConfig();
    }
  }, [dropDownData, orderCount]);

  // 编辑数据回显
  useEffect(() => {
    let vehicleNameList: any[] | null = null;
    if (location.state?.vehicleNameList) {
      vehicleNameList = location.state.vehicleNameList;
    }
    if (vehicleNameList && Array.isArray(vehicleNameList)) {
      const newDefaultValues = vehicleNameList.map((vehicle: any) => ({
        vehicleName: vehicle.vehicleName || '',
        serialNo: vehicle.serialNo || '',
        vehicleTypeId: vehicle.vehicleTypeId || null,
        vehicleNumber: vehicle.vehicleNumber || '',
      }));
      setDefaultValues(newDefaultValues);
      const newVehicleTypeNames: { [key: string]: string } = {};
      vehicleNameList.forEach((vehicle: any, index: number) => {
        if (vehicle.vehicleTypeId) {
          newVehicleTypeNames[vehicle.vehicleTypeId] = vehicle.vehicleTypeName;
        }
      });
      setVehicleTypeNames(newVehicleTypeNames);
    }
  }, [location.state, searchParams]);

  const initializeFormConfig = useCallback(() => {
    const newFormConfigs = Array.from({ length: orderCount }).map(
      (_: any, index: number) => {
        return {
          ...configureVehicleFormConfig,
          fields: configureVehicleFormConfig.fields.map((field) => {
            switch (field.fieldName) {
              case 'vehicleTypeId':
                return {
                  ...field,
                  options: dropDownData.deviceTypeBaseInfoList,
                };
              case 'vehicleName':
                return {
                  ...field,
                  onBlur: (e: any) =>
                    handleVehicleNameBlur(e?.target?.value, index),
                };
              default:
                return field;
            }
          }),
        };
      },
    );
    setFormConfigs(newFormConfigs);
  }, [dropDownData, orderCount]);

  const handleVehicleNameBlur = async (
    vehicleName: string,
    currentEditIndex: number,
  ) => {
    if (!vehicleName || !vehicleName.trim()) return;
    console.log('lastVehicleNameRef.current===', lastVehicleNameRef.current);
    console.log('vehicleName.current===', vehicleName);
    if (vehicleName === lastVehicleNameRef.current) return;
    try {
      const res = await commonApi.getVehicleDetailByName(vehicleName.trim());
      if (res && res.code === HttpStatusCode.Success && res.data) {
        const vehicleData = res.data;
        const formRef = allFormRef.current[currentEditIndex];
        if (formRef && formRef.getFieldsValue) {
          const currentValues = formRef.getFieldsValue();
          formRef.setFieldsValue({
            ...currentValues,
            serialNo: vehicleData.serialNo,
            vehicleTypeId: vehicleData.deviceTypeId,
          });
          // TODO-考虑去掉index
          setVehicleTypeNames((prev) => ({
            ...prev,
            [vehicleData.deviceTypeId]: vehicleData.deviceTypeName,
          }));
        }
        message.success('已自动填充车辆信息');
      } else {
        // 查询失败，清空相关字段
        const formRef = allFormRef.current[currentEditIndex];
        if (formRef && formRef.getFieldsValue) {
          const currentValues = formRef.getFieldsValue();
          formRef.setFieldsValue({
            ...currentValues,
            serialNo: '',
            vehicleTypeId: null,
          });
        }
        message.warning('未找到该车辆信息，请检查车号是否准确');
      }
    } catch (error) {
      console.error('查询车辆信息失败:', error);
      message.error('查询车辆信息失败');
    } finally {
      lastVehicleNameRef.current = vehicleName;
    }
  };

  const handleReset = () => {
    allFormRef.current.forEach((formRef) => {
      if (formRef && formRef.resetFields) {
        formRef.resetFields();
      }
    });
    setVehicleTypeNames({});
    message.success('已重置所有表单');
  };

  const handleSubmit = async () => {
    if (!Number(orderId)) {
      message.error('订单信息异常');
      return;
    }
    setSubmitting(true);
    try {
      const vehicleList: any[] = [];
      let hasValidationError = false;
      for (let i = 0; i < allFormRef.current.length; i++) {
        const formRef = allFormRef.current[i];
        if (!formRef || !formRef.getFieldsValue) continue;
        const values = formRef.getFieldsValue();
        // 检查是否所有字段都为空
        const isEmpty =
          !values.vehicleName &&
          !values.serialNo &&
          !values.vehicleTypeId &&
          !values.vehicleNumber;
        if (isEmpty) {
          continue;
        }
        // 有字段不为空，需要校验必填项
        try {
          await formRef.validateFields();
          const vehicleTypeName =
            vehicleTypeNames[values.vehicleTypeId] ||
            dropDownData.deviceTypeBaseInfoList?.find(
              (item: any) => item.code === values.vehicleTypeId,
            )?.name ||
            '';
          vehicleList.push({
            vehicleName: values.vehicleName,
            serialNo: values.serialNo,
            vehicleTypeId: values.vehicleTypeId,
            vehicleTypeName: vehicleTypeName,
            vehicleNumber: values.vehicleNumber || '',
          });
        } catch (error: any) {
          if (error.errorFields) {
            // 检查是否是车架号或车型为空的情况
            const emptySerialNo = error.errorFields.some(
              (field: any) => field.name[0] === 'serialNo' && !values.serialNo,
            );
            const emptyVehicleType = error.errorFields.some(
              (field: any) =>
                field.name[0] === 'vehicleTypeId' && !values.vehicleTypeId,
            );
            if (emptySerialNo || emptyVehicleType) {
              message.error(`请检查配车信息${i + 1}的车辆ID是否准确`);
            } else {
              message.error(`配车信息${i + 1}填写有误，请检查`);
            }
          }
          hasValidationError = true;
          break;
        }
      }
      if (hasValidationError) {
        return;
      }
      if (vehicleList.length === 0) {
        message.warning('请至少填写一个配车信息');
        return;
      }
      const response = await vehicleOrderManageApi.assignVehicle({
        id: Number(orderId),
        vehicleList,
      });

      if (response.code === HttpStatusCode.Success) {
        message.success('配车成功');
        navigate('/app/vehicleOrderManage');
      } else {
        message.error(response.message || '配车失败');
      }
    } catch (error) {
      console.error('配车失败:', error);
      message.error('配车失败');
    } finally {
      setSubmitting(false);
    }
  };

  const renderVehicleFormCard = (index: number) => {
    return (
      <Card key={index} className="card">
        <div className="card-header">
          <UnorderedListOutlined className="header-icon" />
          <span className="header-title">配车信息{index + 1}</span>
        </div>
        <div className="card-content">
          <CommonForm
            formConfig={formConfigs[index]}
            layout="inline"
            formType="edit"
            name={`vehicle-form-${index}`}
            defaultValue={defaultValues[index] || {}}
            getFormInstance={(ref) => {
              allFormRef.current[index] = ref;
            }}
          />
        </div>
      </Card>
    );
  };

  return (
    <div className="configure-vehicle">
      <div style={{ padding: '15px 0' }}>
        <BreadCrumb items={breadcrumbItems} />
      </div>
      <Card className="card">
        <div className="info-content">
          <div className="info">
            订单数为&nbsp;<span className="highlight">{orderCount}</span>
          </div>
          <Divider
            type="vertical"
            className="info-divider"
            style={{ margin: '0 8px' }}
          />
          <div className="info">
            车型为&nbsp;<span className="highlight">{vehicleModel}</span>
            ，请为该订单配车！需配几辆车，则填写几个卡片即可提交。
          </div>
        </div>
      </Card>
      <div className="vehicle-forms">
        {Array.from({ length: orderCount }).map((_, index) =>
          renderVehicleFormCard(index),
        )}
      </div>
      <div className="bottom-actions">
        <Button onClick={handleReset}>重置</Button>
        <Button type="primary" loading={submitting} onClick={handleSubmit}>
          提交
        </Button>
      </div>
    </div>
  );
};

export default React.memo(ConfigureVehicle);
