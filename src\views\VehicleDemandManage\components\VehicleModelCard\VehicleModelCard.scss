.vehicle-model-card {
  width: 100%;
  height: 180px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  border-radius: 8px;
  cursor: pointer;
  position: relative;
  margin-bottom: 16px;
  transition: all 0.3s ease;
  border: 2px solid transparent;

  &:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  &.active {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  .card-content {
    position: absolute;
    bottom: 5%;
    left: 3%;
    right: 3%;
    background-color: #fff;
    border-radius: 8px;
    padding: 8px;
  }

  .card-info {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .left-info {
      display: flex;
      align-items: center;
      gap: 8px;

      .icon {
        font-size: 12px;
      }

      .name {
        font-size: 12px;
        font-weight: 500;
        max-width: 120px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .right-info {
      display: flex;
      align-items: center;
      gap: 8px;

      .count {
        font-size: 12px;
        font-weight: 600;
      }

      .check-icon {
        font-size: 16px;
        color: #4bce86;
      }
    }
  }
}

// 非激活状态的透明度处理
.vehicle-model-list:has(.vehicle-model-card.active)
  .vehicle-model-card:not(.active) {
  opacity: 0.6;
}
