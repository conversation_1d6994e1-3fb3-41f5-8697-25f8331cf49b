import React, { useState, useEffect, useMemo } from 'react';
import { HttpStatusCode } from '@/fetch/core/constant';
import XMapManageFetch from '../service';
import { isNullObject, showMsg } from '@/utils/utils';
import { XMapInfoDataType } from './type';

const fetchApi = new XMapManageFetch();
export const useInfoData = ({
  currentPage,
  pageSize,
  mapId,
}: {
  currentPage: number;
  pageSize: number;
  mapId?: number | null;
}) => {
  const [infoData, setInfoData] = useState<XMapInfoDataType[]>([]);
  const [total, setTotal] = useState<number>(0);
  const fetchInfoData = async ({
    mapId,
    currentPage,
    pageSize,
  }: {
    currentPage: number;
    pageSize: number;
    mapId?: number | null;
  }) => {
    const res = await fetchApi.fetchXMapInfoData({
      currentPage,
      pageSize,
      mapId,
    });
    if (res.code === HttpStatusCode.Success) {
      if (!isNullObject(res.data)) {
        setInfoData(res.data?.list);
        setTotal(res.data?.total);
      } else {
        setInfoData([]);
        setTotal(0);
        showMsg({
          msg: res.message,
          type: 'success',
        });
      }
    } else {
      setInfoData([]);
      setTotal(0);
      showMsg({
        msg: res.message,
        type: 'error',
      });
    }
    return res;
  };
  useEffect(() => {
    fetchInfoData({ currentPage, pageSize, mapId });
  }, [currentPage, pageSize, mapId]);

  return {
    infoData: infoData,
    total: total,
  };
};
export const useMapIdList = () => {
  const [totalPageCount, setTotalPageCount] = useState<number>(0);
  const [totalInfoData, setTotalInfoData] = useState<XMapInfoDataType[]>([]);
  const fetchInfoData = async (currentPage: number, pageSize: number) => {
    const res = await fetchApi.fetchXMapInfoData({ currentPage, pageSize });
    if (res.code === HttpStatusCode.Success) {
      if (!isNullObject(res.data)) {
        setTotalPageCount(res.data?.pages);
      } else {
        setTotalPageCount(0);
        showMsg({
          msg: res.message,
          type: 'success',
        });
      }
    } else {
      setTotalPageCount(0);
      showMsg({
        msg: res.message,
        type: 'error',
      });
    }
    return res;
  };
  useEffect(() => {
    fetchInfoData(1, 100);
  }, []);

  useEffect(() => {
    const temp: any = [];
    for (let i = 1; i <= totalPageCount; i++) {
      fetchInfoData(i, 100).then((res) => {
        if (res.code === HttpStatusCode.Success) {
          temp.push(...res.data.list);
        }
      });
    }
    setTimeout(() => {
      setTotalInfoData(temp);
    }, 1200);
  }, [totalPageCount]);
  const mapIdList = useMemo(() => {
    const temp = totalInfoData.map((item) => {
      return {
        value: item.id,
        label: `${item.mapBlockName}(${item.id})`,
      };
    });
    return temp.toSorted((a, b) => Number(a.value) - Number(b.value));
  }, [totalInfoData]);
  return mapIdList;
};
