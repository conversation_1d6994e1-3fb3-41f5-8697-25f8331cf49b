/* eslint-disable no-unused-vars */

// eslint-disable-next-line no-unused-vars
import { EditFormTypeInit } from './type';

export enum TabType {
  VEHICLE_USING = 'vehicleUsing',
  VEHICLE_SCHEDULE = 'vehicleSchedule',
  VEHICLE_PRODUCTION = 'vehicleProduction',
  VEHICLE_REPAIRING = 'vehicleRepairing',
}
export enum IsQualified {
  QUALIFIED = '1',
  UNQUALIFIED = '0',
}
export enum BindStoppointTab {
  BINDCURRENTSTOPPOINT = 'BINDCURRENTSTOPPOINT',
  BINDCROSSSTOPPOINT = 'BINDCROSSSTOPPOINT',
}
export const symbolMap = new Map([
  ['le', '≤'],
  ['ge', '≥'],
  ['between', '~'],
]);
// 车辆归属放
export enum vehicleOwnerUseCase {
  OPEN = 'OPEN', // 产品运营
  // SCHOOL = 'SCHOOL', // 园区运营
  PRODUCT = 'PRODUCT', // 生产
  SCHEDULE = 'SCHEDULE', // 调度
  RD = 'RD', // 研发
  TEST = 'TEST', // 测试
  HARDWARE = 'HARDWARE', // 硬件
  FIX = 'FIX', // 维修
  OUTER = 'OUTER', // 销售
  // MODEL = 'MODEL', // 造型
  OTHER = 'OTHER', // 其他分公司
  SOLUTION = 'SOLUTION', // 解决方案
  BUSINESS_LEASING = 'BUSINESS_LEASING', // 商务租赁
  JD_EDUCATION = 'JD_EDUCATION', // 京东教育
  INNOVATE = 'INNOVATE', // 创新组
  STATIC_DISPLAY = 'STATIC_DISPLAY', // 静态展示
}
// 站点用途
export enum stationCaseCode {
  SCHEDULE_CENTER = 'SCHEDULE_CENTER', // 车辆调度中心
  OPEN_OPERATION = 'OPEN_OPERATION', // 快递配送
  PRODUCTION_FACTORY = 'PRODUCTION_FACTORY', // 车辆生产工厂
  CAMPUS_OPERATION = 'CAMPUS_OPERATION', // 校园配送
  QA = 'QA', // QA测试场
  FIX_CENTER = 'FIX_CENTER', // 车辆维修中心
  OUTER_MARKER = 'OUTER_MARKER', // 园区配送-标品
  TIMELY_DELIVERY = 'TIMELY_DELIVERY', // 即时配
  DEMONSTRATE = 'DEMONSTRATE', // 展示演示
  PARK_DELIVERY = 'PARK_DELIVERY', // 园区配送
}
export const girdCrumbItems = [
  {
    title: '车辆管理',
    route: '/app/vehicleManagement',
  },
  {
    title: '车辆配置',
    route: '',
  },
  {
    title: '格口调整',
    route: '',
  },
];

export const scheduleCrumbItems = [
  {
    title: '车辆管理',
    route: '/app/vehicleManagement',
  },
  {
    title: '车辆配置',
    route: '',
  },
  {
    title: '格口调整',
    route: '',
  },
];

export enum dropDownKey {
  VEHICLE_TYPE = 'VEHICLE_TYPE', // 车型类型
  VEHICLE_HARDWARE_STATUS = 'VEHICLE_HARDWARE_STATUS', // 车辆生命周期
  VEHICLE_CHECK_STATUS = 'VEHICLE_CHECK_STATUS', // 标定检测状态
  VEHICLE_OWNER_USE_CASE = 'VEHICLE_OWNER_USE_CASE', // 车辆归属方
  VEHICLE_BUSINESS_TYPE = 'VEHICLE_BUSINESS_TYPE', // 车辆类型
  DEFAULT_STATUS = 'DEFAULT_STATUS', // 存在未完成维修单
}

export enum dropDownListKey {
  VEHICLE_TYPE = 'vehicleTypeList', // 车型类型
  VEHICLE_HARDWARE_STATUS = 'vehicleHardwareStatusList', // 车辆生命周期
  VEHICLE_CHECK_STATUS = 'vehicleCheckStatusList', // 标定检测状态
  VEHICLE_OWNER_USE_CASE = 'vehicleOwnerUseCaseList', // 车辆归属方
  VEHICLE_BUSINESS_TYPE = 'vehicleBusinessTypeList', // 车辆类型
  DEFAULT_STATUS = 'defaultStatusList', // 存在未完成维修单
}

export enum BindCrossStopPageType {
  SINGLE = 'SINGLE', // 单车
  BATCH = 'BATCH', // 多车
}

// 车辆配置，数据初始化格式
export const basic: EditFormTypeInit = {
  basicForm: {
    id: 0,
    name: '',
    boxTemplateId: '',
    boxTemplateName: '',
    gridList: [],
  },
  businessForm: {
    statusId: '',
    statusName: '',
    countryId: '',
    countryName: '',
    provinceId: '',
    provinceName: '',
    cityId: '',
    cityName: '',
    stationId: '',
    stationName: '',
    stationUseCaseId: '',
    stationUseCaseName: '',
    businessNumber: '',
    vehicleBusinessType: '',
  },
  linkPoint: {
    homeList: [],
    loadList: [],
    pickList: [],
    unloadList: [],
  },
};

export const searchFormInit = {
  searchForm: {
    countryId: null,
    stateId: null,
    cityId: null,
    stationId: null,
    vehicleTypeId: null,
    // vehicleStatusId: null,
    name: null,
    // monitorStatus: null,
    ownerUseCaseList: [],
    hardwareStatusList: [],
    // hardwareStatus: null,
    checkStatus: null,
  },
  activeTabKey: TabType.VEHICLE_PRODUCTION,
  // current: 1,
  // pageSize: 10,
};

// 车辆生命周期
export enum vehicleStatus {
  NEW = 'NEW', // 新建
  LEAVE = 'LEAVE', // 待出厂
  REJECT = 'REJECT', // 调度驳回
  RECEIVE = 'RECEIVE', // 待接收
  RESERVE = 'RESERVE', // 待交付
  UNBIND = 'UNBIND', // 解绑待接收
  REPAIRED = 'REPAIRED', // 修完待接收
  USE = 'USE', // 已交付
  MAINTAIN = 'MAINTAIN', // 待维修
  MAINTAINING = 'MAINTAINING', // 维修中
}

// 点位类型枚举
export enum StopPointType {
  HOME = 'homeList',
  PICKUP = 'pickList',
  LOAD = 'loadList',
  UNLOAD = 'unloadList',
  VENDING = 'vendingList',
  STRADDLE = 'straddleList',
  COLLECT = 'collectList',
}

export const StopPointKeyMap = new Map([
  ['homeList', 'HOME'],
  ['pickList', 'PICKUP'],
]);

// 页面类型枚举
export enum PageType {
  productBindStation = 'productBindStation', // 车辆生产-批量绑站
  scheduleChangeStation = 'scheduleChangeStation', // 车辆调度-转站/批量转站
  scheduleReceiveStation = 'scheduleReceiveStation', // 车辆调度-接收/批量接收
  scheduleDeliverStation = 'scheduleDeliverStation', // 车辆调度-交付/批量交付
  usingChangeStation = 'usingChangeStation', // 车辆交付-转站/批量转站
  repair = 'repair', // 车辆维修-转维修中心
}
