import { ProductType } from '@/utils/enum';
import { request } from '../core';

export class RobotMapApi {
  fetchStationList = () => {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/indoorMap/web/warehouse/get_warehouse_list',
      body: {
        productKey: ProductType.INTEGRATE,
      },
    };
    return request(options);
  };

  // 分页查询数据列表
  fetchTableList = ({
    pageNum,
    pageSize,
    searchForm,
  }: {
    pageNum: number;
    pageSize: number;
    searchForm: Object;
  }) => {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/indoorMap/web/mapInfo/map_info_get_page_list',
      body: {
        ...searchForm,
        pageNum,
        pageSize,
        productKey: ProductType.INTEGRATE,
      },
    };
    return request(options);
  };

  fetchVersionList = ({
    pageNum,
    pageSize,
    number,
  }: {
    pageNum: number;
    pageSize: number;
    number: string;
  }) => {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/indoorMap/web/mapInfo/map_version_info_get_page_list',
      body: { mapNumber: number, pageNum, pageSize },
    };
    return request(options);
  };

  fetchMapElement = ({
    version,
    number,
  }: {
    version: string;
    number: string;
  }) => {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/indoorMap/web/mapInfo/get_map_element',
      body: {
        version,
        number,
      },
    };
    return request(options);
  };

  fetchMapInfo = (number: string, version: string) => {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/indoorMap/web/mapInfo/get_map_info',
      body: {
        number,
        version,
      },
    };
    return request(options);
  };
}
