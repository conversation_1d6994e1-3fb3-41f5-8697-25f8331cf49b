import React, { useState, useEffect } from 'react';
import { DownloadOutlined } from '@ant-design/icons';
import { message } from 'antd';
import { mapManageApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';
import './index.scss';

interface DownloadMapModalProps {
  selectedTaskIds: number[];
}

const DownloadMapModal: React.FC<DownloadMapModalProps> = ({
  selectedTaskIds,
}) => {
  const [routeDescUrl, setRouteDescUrl] = useState<string>('');
  const [txtFileUrl, setTxtFileUrl] = useState<string>('');

  useEffect(() => {
    fetchDownloadUrls();
  }, [JSON.stringify(selectedTaskIds)]);

  const fetchDownloadUrls = async () => {
    try {
      const [routeRes, txtRes] = await Promise.all([
        mapManageApi.downloadTaskRoute({ taskId: selectedTaskIds }),
        mapManageApi.downloadShpFile({ taskId: selectedTaskIds }),
      ]);

      if (
        routeRes &&
        routeRes.code === HttpStatusCode.Success &&
        routeRes.data
      ) {
        setRouteDescUrl(routeRes.data);
      } else {
        message.error(routeRes.message || '获取线路文本描述下载地址失败');
      }

      if (txtRes && txtRes.code === HttpStatusCode.Success && txtRes.data) {
        setTxtFileUrl(txtRes.data);
      } else {
        message.error(txtRes.message || '获取txt文件下载地址失败');
      }
    } catch (error) {
      console.error('获取下载地址失败:', error);
      message.error('获取下载地址失败');
    }
  };

  const handleDownload = (url: string) => {
    if (!url) {
      message.error('下载地址不可用');
      return;
    }
    window.open(url, '_self');
  };

  return (
    <div className="download-map-modal">
      <div className="download-cards">
        <div className="download-card">
          <div className="card-description">
            线路文本描述可用于路权申请等业务场景
          </div>
          <div
            className="download-button"
            onClick={() => handleDownload(routeDescUrl)}
          >
            <DownloadOutlined className="download-icon" />
            <span className="download-text">下载线路文本描述</span>
          </div>
        </div>
        <div className="download-card">
          <div className="card-description">
            线路的txt格式文件可用于地图制作参考
          </div>
          <div
            className="download-button"
            onClick={() => handleDownload(txtFileUrl)}
          >
            <DownloadOutlined className="download-icon" />
            <span className="download-text">下载线路txt格式</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DownloadMapModal;
