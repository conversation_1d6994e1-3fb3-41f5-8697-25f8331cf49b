import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Form, Button, message, Input, Modal } from 'antd';
import moment from 'moment';
import {
  CarOutlined,
  FormOutlined,
  DeleteOutlined,
  HistoryOutlined,
} from '@ant-design/icons';
import { CommonForm, FieldItem } from '@jd/x-coreui';
import { BreadCrumb } from '@/components';
import showModal from '@/components/CommonModal';
import VehicleModelCard from '../components/VehicleModelCard/VehicleModelCard';
import OrderConfirmModal from '../components/OrderConfirmModal/OrderConfirmModal';
import UpdateLogModal from '../components/UpdateLogModal/UpdateLogModal';
import {
  detailFormConfig,
  vehicleModelList,
  AuditStatus,
} from '../utils/constant';
import { PageType } from '@/utils/enum';
import { vehicleDemandManageApi, commonApi } from '@/fetch/business';
import { useCommonDropDown } from '@/utils/hooks';
import { HttpStatusCode } from '@/fetch/core/constant';
import './index.scss';

const VehicleDemandDetail: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [alternateForm] = Form.useForm();
  const formRef = useRef<any>(null);

  const type =
    searchParams.get('type')?.toLocaleUpperCase() || PageType.READONLY;
  const id = searchParams.get('id');
  const status = searchParams.get('status');

  const [vehicleModels, setVehicleModels] = useState(vehicleModelList);
  const [selectedVehicleModel, setSelectedVehicleModel] = useState<string>('');
  const [formConfig, setFormConfig] = useState(detailFormConfig);
  const [showAlternateContact, setShowAlternateContact] = useState(false);
  const [detailData, setDetailData] = useState<any>(null);
  const [contactErp, setContactErp] = useState<string>(''); // 维护联系人ERP
  const [alternateErp, setAlternateErp] = useState<string>(''); // 维护备选联系人ERP

  const stationUseCaseOptions = useCommonDropDown(['STATION_USE_CASE']);

  useEffect(() => {
    if (type !== PageType.ADD && id) {
      loadDetailData();
    }
    if (stationUseCaseOptions?.stationUseCaseList?.length > 0) {
      initializeFormConfig();
    }
  }, [type, id, status, stationUseCaseOptions]);

  const initializeFormConfig = async () => {
    try {
      // 获取下拉框数据
      const res = await commonApi.getProvinceCityCountryList();
      const provinceCityData =
        res && res.code === HttpStatusCode.Success && res.data ? res.data : [];
      const shouldDisableFields =
        type === PageType.EDIT && status === AuditStatus.PLACED;

      const newFormConfig = {
        ...detailFormConfig,
        fields: detailFormConfig.fields.map((field: FieldItem) => {
          const newField = {
            ...field,
            type: type === PageType.READONLY ? 'text' : field.type,
          };
          switch (field.fieldName) {
            case 'contact':
              newField.onBlur =
                type !== PageType.READONLY
                  ? (e) => handleContactBlur(e)
                  : undefined;
              break;
            case 'stationNumber':
              newField.onBlur =
                type !== PageType.READONLY
                  ? (e) => handleStationNumberBlur(e)
                  : undefined;
              newField.disabled = shouldDisableFields;
              break;
            case 'stationName':
            case 'count':
              newField.disabled = shouldDisableFields;
              break;
            case 'provinceCityCountry':
              newField.options = provinceCityData;
              newField.formatFieldValue = (value) => {
                return detailData?.countryName || '-';
              };

              break;
            case 'stationUseCase':
              newField.options = stationUseCaseOptions.stationUseCaseList;
              newField.formatFieldValue = () => {
                return detailData?.stationUseCaseName || '-';
              };
              break;
            case 'expectedDeliveryMonth':
              newField.hidden =
                status === AuditStatus.REVIEW || type === PageType.ADD;
              break;
            default:
              break;
          }
          return newField;
        }),
      };
      setFormConfig(newFormConfig);
    } catch (error) {
      console.error('初始化表单配置失败:', error);
      message.error('初始化表单配置失败');
    }
  };

  const echoData = (data: any) => {
    // 恢复主表单数据
    if (formRef.current && data) {
      const formValues = {
        ...data,
        provinceCityCountry:
          data.provinceId && data.cityId && data.countryId
            ? [data.provinceId, data.cityId, data.countryId]
            : undefined,
        expectedDeliveryMonth: data.expectedDeliveryMonth
          ? moment(data.expectedDeliveryMonth, 'YYYY-MM')
          : null,
      };
      formRef.current.setFieldsValue(formValues);
    }
    // 恢复备选联系人数据
    if (data?.alternateContact || data?.alternateContactPhone) {
      setShowAlternateContact(true);
      alternateForm.setFieldsValue({
        alternateContact: data.alternateContact || '',
        alternateContactPhone: data.alternateContactPhone || '',
      });
    } else {
      setShowAlternateContact(false);
      alternateForm.resetFields();
    }
    // 恢复车辆型号选择
    if (data?.vehicleModelType) {
      setSelectedVehicleModel(data.vehicleModelType);
      updateVehicleModelSelection(data.vehicleModelType, data.count || 0);
    } else {
      setSelectedVehicleModel('');
      setVehicleModels(vehicleModelList);
    }
    // 维护联系人和备选联系人ERP
    if (data?.contactErp) {
      setContactErp(data.contactErp);
    }
    if (data?.alternateContactErp) {
      setAlternateErp(data.alternateContactErp);
    }
  };

  const loadDetailData = async () => {
    if (!Number(id)) return;
    try {
      const res = await vehicleDemandManageApi.getVehicleDemandDetail(
        Number(id),
      );
      if (res && res.code === HttpStatusCode.Success && res.data) {
        const data = res.data;
        setDetailData(data);
        echoData(data);
      } else {
        message.error(res.message || '获取详情失败');
      }
    } catch (error) {
      console.error('加载详情数据失败:', error);
      message.error('加载详情数据失败');
    }
  };

  const updateVehicleModelSelection = (
    vehicleModelType: string,
    count: number,
  ) => {
    const updatedModels = vehicleModels.map((model) => ({
      ...model,
      isActive: model.vehicleModelType === vehicleModelType,
      count: model.vehicleModelType === vehicleModelType ? count : 0,
    }));
    setVehicleModels(updatedModels);
  };

  const handleVehicleModelSelect = (model: any) => {
    if (type === PageType.READONLY) return;
    const shouldDisableVehicleModel =
      type === PageType.EDIT && status === AuditStatus.PLACED;
    if (shouldDisableVehicleModel) return;

    setSelectedVehicleModel(model.vehicleModelType);
    const currentCount = formRef.current?.getFieldValue('count') || 0;
    updateVehicleModelSelection(model.vehicleModelType, currentCount);
  };

  const handleFormValueChange = (values: any, changedFieldName: string) => {
    if (changedFieldName === 'count' && selectedVehicleModel) {
      updateVehicleModelSelection(selectedVehicleModel, values.count || 0);
    }
  };

  // 联系人数据变更需要调接口带出联系人其他信息和站点信息
  const handleContactBlur = async (e: any) => {
    const inputErp = e?.target?.value?.trim();
    if (!inputErp || type === PageType.READONLY) return;
    try {
      const response = await commonApi.getStationInfoByErp(inputErp);
      if (response.code === HttpStatusCode.Success && response.data) {
        const userData = response.data;
        setContactErp(inputErp);
        if (formRef.current) {
          const currentValues = formRef.current.getFieldsValue();
          let stationInfo = {};
          if (type === PageType.ADD || status !== AuditStatus.PLACED) {
            stationInfo = {
              stationName: userData.stationName,
              stationNumber: userData.stationNumber,
            };
          }
          formRef.current.setFieldsValue({
            ...currentValues,
            ...stationInfo,
            contact: userData.name,
            mail: userData.mail,
            provinceCityCountry: [
              userData.provinceId,
              userData.cityId,
              userData.countryId,
            ],
            contactPhone: userData.phone,
            address: userData.address,
          });
        }
        message.success('已自动填充用户信息');
      } else {
        setContactErp('');
        message.warning('未找到该用户信息，请手动填写');
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
      setContactErp('');
      message.error('获取用户信息失败');
    }
  };

  // 站点编号变更需要调接口带出站点信息
  const handleStationNumberBlur = async (e: any) => {
    const stationNumber = e?.target?.value?.trim();
    if (!stationNumber || type === PageType.READONLY) return;
    try {
      const response = await commonApi.getQlStationInfoByNumber(stationNumber);
      if (response.code === HttpStatusCode.Success && response.data) {
        const stationData = response.data;
        if (formRef.current) {
          const currentValues = formRef.current.getFieldsValue();
          formRef.current.setFieldsValue({
            ...currentValues,
            stationName: stationData.stationName,
            provinceCityCountry: [
              stationData.provinceId,
              stationData.cityId,
              stationData.countryId,
            ],
            address: stationData.address,
          });
        }
        message.success('已自动填充站点信息');
      } else {
        message.warning('未找到该站点信息，请手动填写');
      }
    } catch (error) {
      console.error('获取站点信息失败:', error);
      message.error('获取站点信息失败');
    }
  };

  // 备选联系人数据变更需要调接口带出联系人其他信息
  const handleAlternateContactBlur = async (e: any) => {
    const inputErp = e?.target?.value?.trim();
    if (!inputErp || type === PageType.READONLY) return;
    try {
      const response = await commonApi.getStationInfoByErp(inputErp);
      if (response.code === HttpStatusCode.Success && response.data) {
        const userData = response.data;
        setAlternateErp(inputErp);
        alternateForm.setFieldsValue({
          alternateContact: userData.name,
          alternateContactPhone: userData.phone,
        });
        message.success('已自动填充备选联系人信息');
      } else {
        setAlternateErp('');
        message.warning('未找到该用户信息，请手动填写');
      }
    } catch (error) {
      console.error('获取备选联系人信息失败:', error);
      setAlternateErp('');
      message.error('获取备选联系人信息失败');
    }
  };

  const getBreadcrumbTitle = () => {
    switch (type) {
      case PageType.ADD:
        return '新增用车需求';
      case PageType.EDIT:
        return '编辑用车需求';
      default:
        return '查看用车需求';
    }
  };
  const breadcrumbItems = [
    {
      title: '需求管理',
      route: '/app/vehicleDemandManage',
    },
    {
      title: getBreadcrumbTitle(),
      route: '',
    },
  ];

  // 渲染底部按钮
  const renderBottomButtons = () => {
    if (type === PageType.READONLY) {
      // 查看态按钮
      const buttons: React.ReactElement[] = [];
      if (status === AuditStatus.REVIEW) {
        buttons.push(
          <Button key="edit" onClick={() => handleEdit()}>
            编辑
          </Button>,
          <Button key="order" type="primary" onClick={() => handleOrder()}>
            下单
          </Button>,
          <Button key="reject" danger onClick={() => handleReject()}>
            驳回
          </Button>,
        );
      } else if (status === AuditStatus.REJECTED) {
        buttons.push(
          <Button key="edit" onClick={() => handleEdit()}>
            编辑
          </Button>,
          <Button
            key="reapprove"
            type="primary"
            onClick={() => handleReapprove()}
          >
            重新提交
          </Button>,
        );
      } else if (status === AuditStatus.PLACED) {
        buttons.push(
          <Button key="edit" onClick={() => handleEdit()}>
            编辑
          </Button>,
        );
      }
      return buttons;
    } else if (type === PageType.ADD) {
      // 新增态按钮
      return [
        <Button key="reset" onClick={() => handleReset()}>
          重置
        </Button>,
        <Button key="submit" type="primary" onClick={() => handleSubmit()}>
          提交
        </Button>,
      ];
    } else if (type === PageType.EDIT) {
      // 编辑态按钮
      const buttons: React.ReactElement[] = [];
      if (status === AuditStatus.REJECTED || status === AuditStatus.REVIEW) {
        buttons.push(
          <Button key="delete" danger onClick={() => handleDelete()}>
            删除需求
          </Button>,
        );
      }
      buttons.push(
        <Button key="reset" onClick={() => handleReset()}>
          重置
        </Button>,
        <Button key="submit" type="primary" onClick={() => handleSubmit()}>
          提交
        </Button>,
      );
      return buttons;
    }
    return [];
  };

  // 按钮处理函数
  const handleEdit = () => {
    navigate(
      `/app/vehicleDemandManage/detail?type=EDIT&id=${id}&status=${status}`,
    );
  };

  const handleOrder = () => {
    if (!detailData) {
      message.error('数据异常，无法下单');
      return;
    }
    const selectedModel = vehicleModelList.find(
      (model) => model.vehicleModelType === detailData.vehicleModelType,
    );
    const orderItem = {
      requirementId: Number(id),
      vehicleModelType: detailData.vehicleModelType,
      vehicleModelName: selectedModel?.name || detailData.vehicleModelType,
      count: detailData.count,
      stationName: detailData.stationName,
      contact: detailData.contact,
      contactPhone: detailData.contactPhone,
      stationNumber: detailData.stationNumber,
      address: detailData.address,
    };
    let orderConfirmRef: any = null;
    showModal({
      title: '需求下单确认',
      width: 800,
      content: (
        <OrderConfirmModal
          orderList={[orderItem]}
          onConfirm={(orderData) => {
            handleOrderConfirm(orderData);
          }}
          ref={(ref) => (orderConfirmRef = ref)}
        />
      ),
      footer: {
        showOk: true,
        showCancel: true,
        okText: '确定下单',
        cancelText: '取消',
        okFunc: async (closeModal) => {
          try {
            if (orderConfirmRef?.handleSubmit) {
              await orderConfirmRef.handleSubmit();
              closeModal();
            }
          } catch (error) {
            // 表单验证失败，不关闭弹窗
            console.error('下单验证失败:', error);
          }
        },
        cancelFunc: (closeModal) => {
          closeModal();
        },
      },
    });
  };

  const handleOrderConfirm = async (orderData: any[]) => {
    try {
      const response = await vehicleDemandManageApi.submitOrder({
        submitOrderList: orderData,
      });
      if (response.code === HttpStatusCode.Success) {
        message.success('下单成功');
        navigate('/app/vehicleDemandManage');
      } else {
        message.error(response.message || '下单失败');
      }
    } catch (error) {
      console.error('下单失败:', error);
      message.error('下单失败');
    }
  };

  const handleReject = () => {
    Modal.confirm({
      title: '确认驳回',
      content: '确定要驳回此需求吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          const response = await vehicleDemandManageApi.rejectRequirement({
            requirementIdList: [Number(id)],
          });
          if (response.code === HttpStatusCode.Success) {
            message.success('驳回成功');
            navigate('/app/vehicleDemandManage');
          } else {
            message.error(response.message || '驳回失败');
          }
        } catch (error) {
          console.error('驳回失败:', error);
          message.error('驳回失败');
        }
      },
    });
  };

  const handleReapprove = () => {
    Modal.confirm({
      title: '确认重新提交',
      content: '确定要重新提交此需求进行审批吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          const response = await vehicleDemandManageApi.resubmitRequirement({
            requirementId: Number(id),
          });
          if (response.code === HttpStatusCode.Success) {
            message.success('重新提交成功');
            navigate('/app/vehicleDemandManage');
          } else {
            message.error(response.message || '重新提交失败');
          }
        } catch (error) {
          console.error('重新提交失败:', error);
          message.error('重新提交失败');
        }
      },
    });
  };

  const handleDelete = () => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除此需求吗？删除后无法恢复。',
      okText: '确定',
      cancelText: '取消',
      okType: 'danger',
      onOk: async () => {
        try {
          const response = await vehicleDemandManageApi.deleteRequirement({
            requirementId: Number(id),
          });
          if (response.code === HttpStatusCode.Success) {
            message.success('删除成功');
            navigate('/app/vehicleDemandManage');
          } else {
            message.error(response.message || '删除失败');
          }
        } catch (error) {
          console.error('删除失败:', error);
          message.error('删除失败');
        }
      },
    });
  };

  const handleShowUpdateLog = () => {
    if (!id) {
      message.error('需求编号不能为空');
      return;
    }

    showModal({
      title: '修改记录',
      width: 1000,
      content: <UpdateLogModal requirementId={Number(id)} />,
      footer: {
        showOk: false,
        showCancel: true,
        cancelText: '关闭',
        cancelFunc: (closeModal) => {
          closeModal();
        },
      },
    });
  };

  const handleReset = () => {
    if (type === PageType.ADD) {
      // 新增态：清空所有数据
      if (formRef.current) {
        formRef.current.resetFields();
      }
      alternateForm.resetFields();
      setShowAlternateContact(false);
      setSelectedVehicleModel('');
      setVehicleModels(vehicleModelList);
    } else if (type === PageType.EDIT) {
      // 编辑态：恢复到初始回显数据
      echoData(detailData);
    }
  };

  const handleSubmit = async () => {
    try {
      const mainFormValues = await formRef.current?.validateFields();
      const { provinceCityCountry, ...otherValues } = mainFormValues;
      let alternateValues = {};
      if (showAlternateContact) {
        const alternateInfo = await alternateForm.validateFields();
        alternateValues = {
          ...alternateInfo,
          alternateErp: alternateErp || '', // 添加备选联系人ERP字段
        };
      }
      if (!selectedVehicleModel) {
        message.error('请选择车辆型号');
        return;
      }
      let expectedDeliveryMonthInfo = {};
      if (type === PageType.EDIT) {
        expectedDeliveryMonthInfo = {
          expectedDeliveryMonth: otherValues?.expectedDeliveryMonth
            ? otherValues.expectedDeliveryMonth?.format('YYYY-MM') // 转换回字符串
            : '',
        };
      }
      const submitData = {
        ...otherValues,
        ...alternateValues,
        ...expectedDeliveryMonthInfo,
        vehicleModelType: selectedVehicleModel,
        provinceId: provinceCityCountry?.[0],
        cityId: provinceCityCountry?.[1],
        countryId: provinceCityCountry?.[2],
        contactErp: contactErp || '',
      };
      let res: any;
      if (type === PageType.ADD) {
        res = await vehicleDemandManageApi.addVehicleDemand(submitData);
      } else if (type === PageType.EDIT) {
        if (!Number(id) || !Number(detailData.version)) {
          message.error('编辑数据异常');
          return;
        }
        console.log('此时的submitData', submitData);
        res = await vehicleDemandManageApi.editVehicleDemand({
          requirementId: Number(id),
          version: Number(detailData.version),
          currentData: submitData,
        });
      }
      if (res?.code === HttpStatusCode.Success) {
        message.success(type === PageType.ADD ? '新增成功' : '编辑成功');
        navigate('/app/vehicleDemandManage');
      } else {
        message.error(res?.message || '操作失败');
      }
    } catch (error) {
      console.error('提交失败:', error);
      message.error('请检查表单填写是否完整');
    }
  };

  return (
    <div className="vehicle-demand-detail">
      <BreadCrumb items={breadcrumbItems} />
      <div className="main-content">
        <div className="left-panel">
          <div className="panel-header header-title-container">
            <CarOutlined className="header-icon" />
            <span className="header-title">选择车辆型号</span>
          </div>
          <div className="vehicle-model-list">
            {vehicleModels.map((model, index) => (
              <VehicleModelCard
                key={index}
                name={model.name}
                url={model.url}
                count={model.count}
                isActive={model.isActive}
                onClick={() => handleVehicleModelSelect(model)}
              />
            ))}
          </div>
        </div>
        <div className="right-panel">
          <div className="panel-header right-header">
            <div className="header-title-container">
              <FormOutlined className="header-icon" />
              <span className="header-title">填写用车信息</span>
            </div>
            <div className="action-container">
              {type === PageType.EDIT && (
                <Button
                  icon={<HistoryOutlined />}
                  onClick={() => handleShowUpdateLog()}
                >
                  修改记录
                </Button>
              )}
              {type !== PageType.READONLY && (
                <Button
                  disabled={showAlternateContact}
                  onClick={() => setShowAlternateContact(true)}
                >
                  添加备选联系人
                </Button>
              )}
            </div>
          </div>
          <div className="form-container">
            <CommonForm
              formConfig={formConfig}
              layout="inline"
              formType="edit"
              onResetClick={undefined}
              onSearchClick={undefined}
              getFormInstance={(ref) => {
                formRef.current = ref;
              }}
              onValueChange={handleFormValueChange}
            />
            {showAlternateContact && (
              <>
                <div className="divider" />
                <Form
                  form={alternateForm}
                  layout="inline"
                  disabled={type === PageType.READONLY}
                >
                  <Form.Item name="alternateContact" label="备选联系人">
                    {type === PageType.READONLY ? (
                      <span>{detailData?.alternateContact || '-'}</span>
                    ) : (
                      <Input
                        placeholder="请输入备选联系人"
                        onBlur={handleAlternateContactBlur}
                      />
                    )}
                  </Form.Item>
                  <Form.Item
                    name="alternateContactPhone"
                    label="备选人电话"
                    rules={[
                      {
                        pattern: /^1[3-9]\d{9}$/,
                        message: '请输入正确的手机号码',
                      },
                    ]}
                  >
                    {type === PageType.READONLY ? (
                      <span>{detailData?.alternateContactPhone || '-'}</span>
                    ) : (
                      <Input placeholder="请输入备选人电话" />
                    )}
                  </Form.Item>
                  {type !== PageType.READONLY && (
                    <Button
                      type="link"
                      icon={<DeleteOutlined />}
                      danger
                      onClick={() => {
                        setShowAlternateContact(false);
                        alternateForm.resetFields();
                      }}
                    >
                      删除
                    </Button>
                  )}
                </Form>
              </>
            )}
          </div>
          <div className="bottom-actions">{renderBottomButtons()}</div>
        </div>
      </div>
    </div>
  );
};

export default React.memo(VehicleDemandDetail);
