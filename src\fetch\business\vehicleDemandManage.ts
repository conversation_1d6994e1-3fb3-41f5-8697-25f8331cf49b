import { request } from '@/fetch/core';
import { Method } from '@/fetch/core/constant';

const vehicleDemandPage = {
  code: '0000',
  data: {
    pageNum: 1,
    pageSize: 10,
    pages: 5,
    total: 45,
    countMap: {
      TOTAL: 45,
      REVIEW: 15,
      PLACED: 20,
      REJECTED: 10,
    },
    list: [
      {
        id: 1,
        requirementNumber: 'REQ202401001',
        contryName: '北京-北京市-朝阳区',
        areaName: '北京省区-北京北区',
        stationName: '北京朝阳站',
        stationNumber: 'BJ_CY_001',
        address: '北京市朝阳区建国路88号',
        stationUseCaseName: '快递配送',
        vehicleModelName: 'A型无人配送车',
        count: 5,
        contact: '张三',
        contactPhone: '13800138001',
        alternateContact: '李四',
        alternateContactPhone: '13800138002',
        createTime: '2024-01-15 10:30:00',
        status: 'REVIEW',
        statusName: '审批中',
        reviewerUser: '王五',
        statusModifyTime: '2024-01-15 14:20:00',
      },
      {
        id: 2,
        requirementNumber: 'REQ202401002',
        contryName: '上海-上海市-浦东新区',
        areaName: '上海省区-上海东区',
        stationName: '上海浦东站',
        stationNumber: 'SH_PD_001',
        address: '上海市浦东新区陆家嘴环路1000号',
        stationUseCaseName: '校园配送',
        vehicleModelName: 'B型无人配送车',
        count: 3,
        contact: '赵六',
        contactPhone: '13800138003',
        alternateContact: '钱七',
        alternateContactPhone: '13800138004',
        createTime: '2024-01-16 09:15:00',
        status: 'PLACED',
        statusName: '已下单',
        reviewerUser: '孙八',
        statusModifyTime: '2024-01-16 16:45:00',
      },
      {
        id: 3,
        requirementNumber: 'REQ202401003',
        contryName: '广东-深圳市-南山区',
        areaName: '广东省区-深圳片区',
        stationName: '深圳南山站',
        stationNumber: 'SZ_NS_001',
        address: '深圳市南山区科技园南区',
        stationUseCaseName: '园区配送',
        vehicleModelName: 'C型无人配送车',
        count: 8,
        contact: '周九',
        contactPhone: '13800138005',
        alternateContact: '吴十',
        alternateContactPhone: '13800138006',
        createTime: '2024-01-17 11:20:00',
        status: 'REJECTED',
        statusName: '已驳回',
        reviewerUser: '郑十一',
        statusModifyTime: '2024-01-17 15:30:00',
      },
      // 异常情况：空字段
      {
        id: 4,
        requirementNumber: '',
        contryName: '',
        areaName: '',
        stationName: '',
        stationNumber: '',
        address: '',
        stationUseCaseName: '',
        vehicleModelName: '',
        count: 0,
        contact: '',
        contactPhone: '',
        alternateContact: '',
        alternateContactPhone: '',
        createTime: '',
        status: '',
        statusName: '',
        reviewerUser: '',
        statusModifyTime: '',
      },
      // 异常情况：null值
      {
        id: 5,
        requirementNumber: null,
        contryName: null,
        areaName: null,
        stationName: null,
        stationNumber: null,
        address: null,
        stationUseCaseName: null,
        vehicleModelName: null,
        count: null,
        contact: null,
        contactPhone: null,
        alternateContact: null,
        alternateContactPhone: null,
        createTime: null,
        status: null,
        statusName: null,
        reviewerUser: null,
        statusModifyTime: null,
      },
      // 异常情况：超长字段
      {
        id: 6,
        requirementNumber:
          'REQ202401004_VERY_LONG_REQUIREMENT_NUMBER_FOR_TESTING',
        contryName:
          '这是一个非常非常非常非常非常非常非常非常非常非常长的省市区名称用于测试前端显示效果',
        areaName:
          '这是一个非常非常非常非常非常非常非常非常非常非常长的省区片区名称用于测试前端显示效果',
        stationName:
          '这是一个非常非常非常非常非常非常非常非常非常非常长的站点名称用于测试前端显示效果',
        stationNumber: 'VERY_LONG_STATION_NUMBER_FOR_TESTING_FRONTEND_DISPLAY',
        address:
          '这是一个非常非常非常非常非常非常非常非常非常非常长的详细地址用于测试前端显示效果，包含各种特殊字符@#$%^&*()',
        stationUseCaseName:
          '这是一个非常非常非常非常非常非常非常非常非常非常长的站点用途名称用于测试前端显示效果',
        vehicleModelName:
          '这是一个非常非常非常非常非常非常非常非常非常非常长的车型名称用于测试前端显示效果',
        count: 999999,
        contact:
          '这是一个非常非常非常非常非常非常非常非常非常非常长的联系人姓名用于测试前端显示效果',
        contactPhone: '13800138007890123456789',
        alternateContact:
          '这是一个非常非常非常非常非常非常非常非常非常非常长的备选联系人姓名用于测试前端显示效果',
        alternateContactPhone: '13800138008901234567890',
        createTime: '2024-01-18 23:59:59',
        status: 'REVIEW',
        statusName: '审批中',
        reviewerUser:
          '这是一个非常非常非常非常非常非常非常非常非常非常长的审核人姓名用于测试前端显示效果',
        statusModifyTime: '2024-01-18 23:59:59',
      },
      // 异常情况：特殊字符
      {
        id: 7,
        requirementNumber: 'REQ@#$%^&*()_+{}|:"<>?[]\\;\',./',
        contryName: '特殊字符省市区@#$%^&*()',
        areaName: '特殊字符省区片区@#$%^&*()',
        stationName: '特殊字符站点@#$%^&*()',
        stationNumber: 'SPECIAL_@#$%^&*()_001',
        address: '特殊字符地址@#$%^&*()_+{}|:"<>?[]\\;\',./',
        stationUseCaseName: '特殊字符用途@#$%^&*()',
        vehicleModelName: '特殊字符车型@#$%^&*()',
        count: -1, // 负数异常
        contact: '特殊字符联系人@#$%^&*()',
        contactPhone: '@#$%^&*()',
        alternateContact: '特殊字符备选联系人@#$%^&*()',
        alternateContactPhone: '@#$%^&*()',
        createTime: '无效时间格式',
        status: 'INVALID_STATUS',
        statusName: '无效状态',
        reviewerUser: '特殊字符审核人@#$%^&*()',
        statusModifyTime: '无效时间格式',
      },
      // 异常情况：数字异常
      {
        id: 8,
        requirementNumber: 'REQ202401008',
        contryName: '北京-北京市-海淀区',
        areaName: '北京省区-北京北区',
        stationName: '北京海淀站',
        stationNumber: 'BJ_HD_002',
        address: '北京市海淀区中关村大街1号',
        stationUseCaseName: 'QA测试场',
        vehicleModelName: 'D型无人配送车',
        count: 0, // 零数量异常
        contact: '测试联系人',
        contactPhone: '1380013800', // 不完整手机号
        alternateContact: null,
        alternateContactPhone: null,
        createTime: '2024-01-19 08:00:00',
        status: 'REVIEW',
        statusName: '审批中',
        reviewerUser: '测试审核人',
        statusModifyTime: '2024-01-19 08:00:00',
      },
      // 异常情况：未来时间
      {
        id: 9,
        requirementNumber: 'REQ202401009',
        contryName: '广东-广州市-天河区',
        areaName: '广东省区-广州片区',
        stationName: '广州天河站',
        stationNumber: 'GZ_TH_001',
        address: '广州市天河区珠江新城',
        stationUseCaseName: '即时配',
        vehicleModelName: 'E型无人配送车',
        count: 2,
        contact: '未来联系人',
        contactPhone: '13800138009',
        alternateContact: '未来备选联系人',
        alternateContactPhone: '13800138010',
        createTime: '2025-12-31 23:59:59', // 未来时间
        status: 'PLACED',
        statusName: '已下单',
        reviewerUser: '未来审核人',
        statusModifyTime: '2025-12-31 23:59:59', // 未来时间
      },
      // 异常情况：过去很久的时间
      {
        id: 10,
        requirementNumber: 'REQ199901001',
        contryName: '上海-上海市-徐汇区',
        areaName: '上海省区-上海西区',
        stationName: '上海徐汇站',
        stationNumber: 'SH_XH_002',
        address: '上海市徐汇区漕河泾开发区',
        stationUseCaseName: '展示演示',
        vehicleModelName: '重型配送车',
        count: 1,
        contact: '历史联系人',
        contactPhone: '13800138011',
        alternateContact: '历史备选联系人',
        alternateContactPhone: '13800138012',
        createTime: '1999-01-01 00:00:00', // 很久以前的时间
        status: 'REJECTED',
        statusName: '已驳回',
        reviewerUser: '历史审核人',
        statusModifyTime: '1999-01-01 00:00:00', // 很久以前的时间
      },
    ],
  },
};

// 需求详情Mock数据
const vehicleDemandDetail = {
  code: '0000',
  message: 'ok',
  data: {
    countryName: '北京-北京市-朝阳区',
    provinceId: 1,
    cityId: 11,
    countryId: 111,
    requirementNumber: 'REQ202401001',
    address: '北京市朝阳区建国路88号',
    stationName: '北京朝阳站',
    stationNumber: 'BJ_CY_001',
    stationUseCaseName: '快递配送',
    stationUseCase: 'EXPRESS_DELIVERY',
    vehicleModelType: 'WYZX_W5',
    count: 5,
    contactErp: 'zhangsan',
    contact: '张三',
    contactPhone: '13800138001',
    mail: '<EMAIL>',
    alternateContactErp: 'lisi',
    alternateContact: '李四',
    alternateContactPhone: '13800138002',
    expectedDeliveryMonth: '2024-06',
    version: 1,
  },
};

// 接口参数类型定义
export interface VehicleDemandPageRequest {
  startTime?: string;
  endTime?: string;
  provinceId?: number | null;
  cityId?: number | null;
  countryId?: number | null;
  provinceAgencyCode?: string;
  areaCode?: string;
  stationNumber?: string;
  vehicleModelType?: string;
  stationUseCase?: string;
  status?: string;
  pageNum: number;
  pageSize: number;
}

export interface VehicleDemandItem {
  id: number;
  requirementNumber: string;
  contryName: string;
  areaName: string;
  stationName: string;
  stationNumber: string;
  address: string;
  stationUseCaseName: string;
  vehicleModelName: string;
  count: number;
  contact: string;
  contactPhone: string;
  alternateContact?: string;
  alternateContactPhone?: string;
  createTime: string;
  status: string;
  statusName: string;
  reviewerUser: string;
  statusModifyTime: string;
}

export interface VehicleDemandPageResponse {
  pageNum: number;
  pageSize: number;
  pages: number;
  total: number;
  countMap: {
    TOTAL: number;
    REVIEW: number;
    PLACED: number;
    REJECTED: number;
  };
  list: VehicleDemandItem[];
}

// 新增用车需求接口参数类型
export interface AddVehicleDemandRequest {
  vehicleModelType: string;
  count: number;
  contact: string;
  contactErp?: string;
  contactPhone: string;
  mail: string;
  alternateContact?: string;
  alternateErp?: string;
  alternateContactPhone?: string;
  stationName: string;
  stationUseCase: string;
  stationNumber: string;
  provinceId: number;
  cityId: number;
  countryId: number;
  address: string;
}

// 编辑用车需求接口参数类型
export interface EditVehicleDemandRequest {
  requirementId: number;
  version: number;
  currentData: {
    stationName: string;
    stationNumber: string;
    contact: string;
    contactErp: string;
    contactPhone: string;
    mail?: string;
    address: string;
    stationUseCase: string;
    alternateContact?: string;
    alternateContactPhone?: string;
    count: number;
    vehicleModelType: string;
    expectedDeliveryMonth?: string;
    provinceId: number;
    cityId: number;
    countryId: number;
  };
}

// 需求详情响应类型
export interface VehicleDemandDetailResponse {
  countryName: string;
  provinceId?: number;
  cityId?: number;
  countryId?: number;
  requirementNumber: string;
  address: string;
  stationName: string;
  stationNumber: string;
  stationUseCaseName: string;
  stationUseCase: string;
  vehicleModelType: string;
  count: number;
  contactErp: string;
  contact: string;
  contactPhone: string;
  mail: string;
  alternateContactErp?: string;
  alternateContact?: string;
  alternateContactPhone?: string;
  expectedDeliveryMonth?: string;
  version: number;
}

// 需求下单接口参数类型
export interface SubmitOrderRequest {
  submitOrderList: Array<{
    requirementId: number;
    expectedDeliveryMonth: string;
  }>;
}

// 删除需求接口参数类型
export interface DeleteRequirementRequest {
  requirementId: number;
}

// 需求驳回接口参数类型
export interface RejectRequirementRequest {
  requirementIdList: number[];
}

// 重新提交接口参数类型
export interface ResubmitRequirementRequest {
  requirementId: number;
}

// 修改记录接口参数类型
export interface UpdateLogRequest {
  requirementId: number;
  pageNum: number;
  pageSize: number;
}

// 修改记录响应类型
export interface UpdateLogItem {
  id: number;
  userName: string;
  modifyTime: string;
  fieldName: string;
  beforeValue: string;
  afterValue: string;
}

export interface UpdateLogResponse {
  total: number;
  pageNum: number;
  pageSize: number;
  list: UpdateLogItem[];
}

// 批量上传接口参数类型
export interface BatchSubmitRequest {
  fileKey: string;
}

// 获取下载模板接口参数类型
export interface GetDownloadUrlRequest {
  type: string;
}

class VehicleDemandManageApi {
  // 分页查询用车需求
  public async getVehicleDemandPage(
    params: VehicleDemandPageRequest,
  ): Promise<any> {
    const requestOptions: RequestOptions = {
      path: '/k2/management/deployment/requirement/page',
      method: Method.POST,
      body: params,
    };
    console.log('调用分页查询用车需求下拉框接口了！！', params);
    return Promise.resolve(vehicleDemandPage);
    return request(requestOptions);
  }

  // 需求详情查询
  public async getVehicleDemandDetail(requirementId: number): Promise<any> {
    const requestOptions: RequestOptions = {
      path: '/k2/management/deployment/requirement/getDetailById',
      method: Method.POST,
      body: { requirementId },
    };
    console.log('调用需求详情查询接口了！！', requirementId);
    return Promise.resolve(vehicleDemandDetail);
    return request(requestOptions);
  }

  // 新增用车需求
  public async addVehicleDemand(params: AddVehicleDemandRequest): Promise<any> {
    const requestOptions: RequestOptions = {
      path: '/k2/management/deployment/requirement/add',
      method: Method.POST,
      body: params,
    };
    console.log('调用新增用车需求接口了！！', params);
    return Promise.resolve({ code: '0000', message: 'ok' });
    return request(requestOptions);
  }

  // 编辑用车需求
  public async editVehicleDemand(
    params: EditVehicleDemandRequest,
  ): Promise<any> {
    const requestOptions: RequestOptions = {
      path: '/k2/management/deployment/requirement/edit',
      method: Method.POST,
      body: params,
    };
    console.log('调用编辑用车需求接口了！！', params);
    return Promise.resolve({ code: '0000', message: 'ok' });
    return request(requestOptions);
  }

  // 需求下单
  public async submitOrder(params: SubmitOrderRequest): Promise<any> {
    const requestOptions: RequestOptions = {
      path: '/k2/management/deployment/requirement/submitOrder',
      method: Method.POST,
      body: params,
    };
    console.log('调用需求下单接口了！！', params);
    return Promise.resolve({ code: '0000', message: '下单成功' });
    return request(requestOptions);
  }

  // 删除需求
  public async deleteRequirement(
    params: DeleteRequirementRequest,
  ): Promise<any> {
    const requestOptions: RequestOptions = {
      path: '/k2/management/deployment/requirement/deleteById',
      method: Method.POST,
      body: params,
    };
    console.log('调用删除需求接口了！！', params);
    return Promise.resolve({
      code: '0000',
      message: '删除成功',
      data: {},
    });
    return request(requestOptions);
  }

  // 需求驳回
  public async rejectRequirement(
    params: RejectRequirementRequest,
  ): Promise<any> {
    const requestOptions: RequestOptions = {
      path: '/k2/management/deployment/requirement/reject',
      method: Method.POST,
      body: params,
    };
    console.log('调用需求驳回接口了！！', params);
    return Promise.resolve({ code: '0000', message: '驳回成功' });
    return request(requestOptions);
  }

  // 需求重新提交
  public async resubmitRequirement(
    params: ResubmitRequirementRequest,
  ): Promise<any> {
    const requestOptions: RequestOptions = {
      path: '/k2/management/deployment/requirement/resubmit',
      method: Method.POST,
      body: params,
    };
    console.log('调用需求重新提交接口了！！', params);
    return Promise.resolve({ code: '0000', message: '重新提交成功' });
    return request(requestOptions);
  }

  // 获取修改记录
  public async getUpdateLog(params: UpdateLogRequest): Promise<any> {
    const requestOptions: RequestOptions = {
      path: '/k2/management/deployment/requirement/pageUpdateLog',
      method: Method.POST,
      body: params,
    };
    console.log('调用修改记录接口了！！', params);

    // Mock数据
    const mockData = {
      code: '0000',
      message: 'ok',
      data: {
        total: 3,
        pageNum: 1,
        pageSize: 10,
        list: [
          {
            id: 1,
            userName: '张三',
            modifyTime: '2024-01-20 14:30:45',
            fieldName: '用车数量',
            beforeValue: '5',
            afterValue: '8',
          },
          {
            id: 2,
            userName: '李四',
            modifyTime: '2024-01-19 10:15:20',
            fieldName: '联系电话',
            beforeValue: '13800138001',
            afterValue: '13800138999',
          },
          {
            id: 3,
            userName: '王五',
            modifyTime: '2024-01-18 16:45:30',
            fieldName: '详细地址',
            beforeValue: '北京市朝阳区建国路88号',
            afterValue: '北京市朝阳区建国路99号',
          },
        ],
      },
    };
    return Promise.resolve(mockData);
    return request(requestOptions);
  }

  // 批量上传用车需求
  public async batchSubmit(params: BatchSubmitRequest): Promise<any> {
    const requestOptions: RequestOptions = {
      path: '/k2/management/deployment/requirement/batchSubmit',
      method: Method.POST,
      body: params,
    };
    console.log('调用批量上传用车需求接口了！！', params);
    return Promise.resolve({ code: '0000', message: '批量上传成功' });
    return request(requestOptions);
  }

  // 获取批量需求模板下载链接
  public async getDownloadUrl(params: GetDownloadUrlRequest): Promise<any> {
    const requestOptions: RequestOptions = {
      path: '/k2/management/upload/get_download_url',
      method: Method.POST,
      body: params,
    };
    console.log('调用获取下载模板接口了！！', params);

    // Mock数据
    const mockData = {
      code: '0000',
      message: 'ok',
      data: {
        url: 'https://example.com/template/vehicle_demand_template.xlsx',
      },
    };
    return Promise.resolve(mockData);
    return request(requestOptions);
  }
}

export const vehicleDemandManageApi = new VehicleDemandManageApi();
