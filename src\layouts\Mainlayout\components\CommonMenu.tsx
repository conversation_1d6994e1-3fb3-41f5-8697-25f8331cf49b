import React, { useState, useEffect } from 'react';
import { RootState } from '@/redux/store';
import { useSelector, useDispatch } from 'react-redux';
import { useLocation, useNavigate } from 'react-router-dom';
import { removeSearchValues } from '@/redux/reducer/searchForm';
import { Menu } from 'antd';
import { sendGlobalEvent } from '@/utils/emit';
import { MenuClstag } from '@/utils/menuInfo';
import { customReport } from '@/utils/utils';

interface MenuItem {
  code: string;
  path: string;
  openType?: 'new' | 'iframe' | 'now';
  type: string;
}
const CommonMenu = () => {
  const location = useLocation();
  const dispatch = useDispatch();
  const navigator = useNavigate();
  const { menuData, resourceList } = useSelector(
    (state: RootState) => state.common,
  );
  const [current, setCurrent] = useState<string>('');
  const [openKeys, setOpenKeys] = useState<string[]>([]);

  const curPath =
    location.pathname === '/app/commoniframe'
      ? location.search.split('?')[1].split('=')[1]
      : location.pathname.split('/').splice(0, 3).join('/');

  useEffect(() => {
    const cb = (event) => {
      if (event.data.eventName === 'CHANGE_MENU') {
        navigator(`/app/commoniframe?iframeurl=${event.data.data}`);
        sessionStorage.setItem('iframeUrl', event.data.data);
      }
    };
    window.addEventListener('message', cb, false);
    return () => {
      window.removeEventListener('message', cb);
    };
  }, []);
  useEffect(() => {
    if (resourceList?.length > 0 && curPath) {
      const item: any = resourceList.find((val: any) => val.path === curPath);
      if (!item) return;
      setCurrent(item.code);
      const parents = getParentCodes(item.code);
      item?.code && setOpenKeys(parents.concat(openKeys));
    }
  }, [curPath, resourceList]);

  /**
   * 获取菜单项的所有上级code
   * @param code 菜单项的code，例如 MENU_1_2_3
   * @returns 所有上级code的数组，例如 ['MENU_1', 'MENU_1_2']
   */
  const getParentCodes = (code: string): string[] => {
    if (!code || !code.includes('_')) return [];

    const parts = code.split('_');
    const result: string[] = [];

    // 从最高级开始构建，但不包括自身
    for (let i = 2; i < parts.length; i++) {
      result.push(parts.slice(0, i).join('_'));
    }

    return result;
  };

  const onClick = (val) => {
    dispatch(removeSearchValues(null));
    const resource: any = resourceList.find(
      (item: MenuItem) => item.code === val.key,
    );
    if (!resource) return;
    const clstag = MenuClstag.get(resource.code);
    if (clstag) {
      customReport(clstag);
    }

    switch (resource.openType) {
      case 'new':
        window.open(resource.path, '_blank');
        return;

      case 'iframe':
        navigator(`/app/commoniframe?iframeurl=${val.key}`);
        sessionStorage.setItem('iframeUrl', val.key);
        sendGlobalEvent('forUpdateIframe', val.key);
        return;

      default:
        if (resource.path.split('/')[1] === 'ota') {
          (window as any)._QIANKUN_SDK_?.event.emit(
            'clear-microApp-searchInfo',
            'ota',
          );
        }
        if (resource.path.includes('/')) {
          navigator(resource.path);
        }
    }
  };

  const onOpenChange = (keys) => {
    setOpenKeys(keys);
  };

  return (
    <Menu
      onClick={onClick}
      onOpenChange={onOpenChange}
      mode="inline"
      items={menuData}
      selectedKeys={[current]}
      openKeys={openKeys}
    />
  );
};

export default CommonMenu;
