import { request } from '../core';

export class MaintainAbnormalDurationApi {
  getPageList(params: {
    stationBaseId?: any;
    provinceId?: any;
    cityId?: any;
    deviceName?: any;
    startTime?: any;
    endTime?: any;
    pageNum: number;
    pageSize: number;
  }) {
    return request({
      absoluteURL: `${location.protocol}//${process.env.JDX_APP_CLOUD_FETCH_DOMAIN}/k2/management/device_disabled_record/get_page_list`,
      method: 'POST',
      body: {
        stationBaseId: params?.stationBaseId,
        provinceId: params?.provinceId,
        cityId: params?.cityId,
        deviceName: params?.deviceName,
        startTime: params?.startTime,
        endTime: params?.endTime,
        pageNum: params?.pageNum,
        pageSize: params?.pageSize,
      },
      headers: {
        'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
      },
    });
  }

  editRecord(params: {
    recordId: string;
    breakdown: string | number;
    disabledCauseKey: string;
    notOperationDuration: string;
  }) {
    return request({
      absoluteURL: `${location.protocol}//${process.env.JDX_APP_CLOUD_FETCH_DOMAIN}/k2/management/device_disabled_record/edit_device_disabled_record`,
      method: 'POST',
      headers: {
        'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
      },
      body: params,
    });
  }
  createRecord(params: {
    disabledRecordItems: any[];
    deviceName: string;
    recordMode: string;
    breakdown: string | number;
    disabledCauseKey: string;
    stationBaseId: string;
  }) {
    return request({
      absoluteURL: `${location.protocol}//${process.env.JDX_APP_CLOUD_FETCH_DOMAIN}/k2/management/device_disabled_record/add_device_disabled_record`,
      method: 'POST',
      headers: {
        'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
      },
      body: params,
    });
  }
  getRecordInfo(params: { recordId: any }) {
    return request({
      absoluteURL: `${location.protocol}//${process.env.JDX_APP_CLOUD_FETCH_DOMAIN}/k2/management/device_disabled_record/get_record`,
      method: 'POST',
      headers: {
        'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
      },
      body: params,
    });
  }
  getDisabledCause() {
    return request({
      absoluteURL: `${location.protocol}//${process.env.JDX_APP_CLOUD_FETCH_DOMAIN}/k2/management/device_disabled_record/get_disabled_cause_list`,
      method: 'POST',
      headers: {
        'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
      },
    });
  }
  createDisabledCause(params: { name: string }) {
    return request({
      absoluteURL: `${location.protocol}//${process.env.JDX_APP_CLOUD_FETCH_DOMAIN}/k2/management/device_disabled_record/add_disabled_cause`,
      method: 'POST',
      headers: {
        'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
      },
      body: params,
    });
  }

  getStation() {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/station_base/get_station_address_list',
      body: {},
    };
    return request(options);
  }
  getVehicle() {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/device/get_device_list',
    };
    return request(options);
  }

  getOperateTime() {
    return request({
      method: `POST`,
      path: '/k2/management/require/getOperationTime',
    });
  }
}
