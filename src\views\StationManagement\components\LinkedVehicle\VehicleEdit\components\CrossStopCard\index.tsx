import React, { useEffect, useState } from 'react';
import './index.scss';
import {
  Form,
  Select,
  Button,
  message,
  Modal,
  Table,
  Row,
  Col,
  Popconfirm,
} from 'antd';
import { HttpStatusCode } from '@/fetch/core/constant';
import StopPointNameTag from '../StopPointNameTag';
import { StopPointType, StopPointKeyMap } from '@/utils/constant';
import { DeleteOutlined } from '@ant-design/icons';
import TransferWithTable from '@/components/TransferWithTable';
import { StationFetchApi } from '@/fetch/business';
interface Props {
  info: any; // 每个跨站站点的全部信息（站点信息，home点，取货点信息）
  stationOptions: any[];
  saveInfo: Function;
  crossStopInfo: any[];
}
const CrossStopCard = (props: Props) => {
  const fetchApi = new StationFetchApi();
  const { info, stationOptions, crossStopInfo, saveInfo } = props;
  const [cardForm] = Form.useForm();
  const [homePointForm] = Form.useForm();
  const [selectHomeModal, setSelectHomeModal] = useState<boolean>(false);
  const [selectStopModal, setSelectStopModal] = useState<boolean>(false);
  const [homeModalTitle, setHomeModalTitle] = useState<string | null>(null);
  const [stopModalTitle, setStopModalTitle] = useState<string | null>(null);
  const [allPoint, setAllPoint] = useState<any[]>([]); // 全量home点或取货点
  const [selectedHomeRowKeys, setSelectedHomeRowKeys] = useState<any[]>([]);
  const [selectedHomeRows, setSelectHomeRows] = useState<any[]>([]);
  const [selectedStation, setSelectedStation] = useState<any>();
  const columns: any[] = [
    {
      title: '序号',
      dataIndex: 'order',
      align: 'center',
      width: '10%',
      render: (text: any, record: any, index: number) => `${index + 1}`,
    },
    {
      title: '停靠点名称',
      width: '90%',
      dataIndex: 'name',
      align: 'center',
      ellipsis: true,
      render: (text: any, record: any, index: number) =>
        `${record.name} ${record.waitingTime}分钟`,
    },
  ];
  const selectedStopPoints =
    info.stopList.length > 0 ? info.stopList.map((item: any) => item.id) : []; // 每一栏已选的取货点
  const [targetKeys, setTargetKeys] = useState<any[]>(selectedStopPoints);
  const [stopPointTableLoading, setStopPointTableLoading] =
    useState<boolean>(false);

  useEffect(() => {
    if (info.stationInfo) {
      setTimeout(() => {
        cardForm.setFieldsValue({
          stationInfo: info.stationInfo,
        });
        setSelectedStation(
          stationOptions.find((item: any) => item.value === info.stationInfo),
        );
      }, 100);
    }
  }, [JSON.stringify(info), JSON.stringify(stationOptions)]);

  // 点击添加或者更换按钮去选择停靠点
  const onSelect = (type: string) => {
    const station = cardForm.getFieldsValue(['stationInfo']).stationInfo;
    if (!station) {
      message.error('请先选择站点');
    } else {
      if (type === StopPointType.HOME) {
        setSelectHomeModal(true);
        setHomeModalTitle(selectedStation.label);
      } else if (type === StopPointType.PICKUP) {
        setSelectStopModal(true);
        setStopModalTitle(`${selectedStation.label}添加取货点停靠点`);
      }
      try {
        if (type === StopPointType.PICKUP) setStopPointTableLoading(true);
        fetchApi
          .getStopListByTypeOfStation({
            stationBaseId: selectedStation.value,
            type: StopPointKeyMap.get(type)!,
          })
          .then((res: any) => {
            if (res && res.code === HttpStatusCode.Success) {
              setAllPoint(res.data);
            }
          });
      } catch (e) {
        console.log(e);
      } finally {
        if (type === StopPointType.PICKUP) {
          setStopPointTableLoading(false);
        }
      }
    }
  };

  // 清空当前已选的跨站站点
  const onClearSelect = () => {
    setTargetKeys([]);
    crossStopInfo.forEach((item: any, index: any) => {
      if (item.sort === info.sort) {
        crossStopInfo.splice(index, 1, {
          ...info,
          stationInfo: null,
          homeList: [],
          stopList: [],
        });
      }
    });
    setSelectedStation(null);
    saveInfo([...crossStopInfo]);
  };

  // 选中跨站站点
  const onSubmitSelectedStation = (option: any) => {
    let isRepeat = false;
    setTargetKeys([]);
    if (option) {
      crossStopInfo.forEach((item: any) => {
        if (item.stationInfo?.id === option?.value) {
          isRepeat = true;
          return;
        }
      });
    }
    if (isRepeat) {
      message.error(`该站点${option.label}已存在，不允许重复添加！`);
      cardForm.setFieldsValue({
        stationInfo: null,
      });
    }
    crossStopInfo.forEach((item: any, index: any) => {
      if (item.sort === info.sort) {
        crossStopInfo.splice(index, 1, {
          ...info,
          stationInfo: isRepeat ? null : option?.value,
          homeList: [],
          stopList: [],
        });
      }
    });
    saveInfo([...crossStopInfo]);
    setSelectedStation(option);
  };

  // 选中home点
  const onSubmitSelectedHomePoint = () => {
    if (selectedHomeRows.length <= 0) {
      message.error('请选择home点');
      return;
    }
    const homeValue = [
      {
        id: selectedHomeRows[0].id,
        name: selectedHomeRows[0].name,
        waitingTime: selectedHomeRows[0].waitingTime,
      },
    ];
    crossStopInfo.forEach((item: any, index: any) => {
      if (item.sort === info.sort) {
        crossStopInfo.splice(index, 1, {
          ...info,
          homeList: homeValue,
        });
      }
    });
    saveInfo([...crossStopInfo]);
    setSelectHomeRows([]);
    setSelectedHomeRowKeys([]);
    setSelectHomeModal(false);
    setHomeModalTitle(null);
    setAllPoint([]);
  };

  // 选中取货点
  const onSubmitSelectedStopPoint = () => {
    const stopPointList: any[] = [];
    allPoint.forEach((item: any) => {
      if (targetKeys.indexOf(item.id) > -1) {
        stopPointList.push(item);
      }
    });
    crossStopInfo.forEach((item: any, index: any) => {
      if (item.sort === info.sort) {
        crossStopInfo.splice(index, 1, {
          ...info,
          stopList: stopPointList,
        });
      }
    });
    saveInfo([...crossStopInfo]);
    setSelectStopModal(false);
    setStopModalTitle(null);
    setAllPoint([]);
  };
  return (
    <div className="cross-stop-card">
      <Form form={cardForm} labelCol={{ span: 2 }} wrapperCol={{ span: 22 }}>
        <Form.Item
          label="站点名称"
          name="stationInfo"
          rules={[{ required: true, message: '请选择站点名称' }]}
        >
          <Select
            options={stationOptions}
            placeholder={'请输入站点名称，支持关键字联想全称'}
            allowClear
            showSearch
            filterOption={(input, option) => {
              const label: any = option?.label || '';
              return label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
            }}
            onChange={(value, option) => onSubmitSelectedStation(option)}
            onClear={onClearSelect}
          />
        </Form.Item>
        <Form.Item label="home点" required>
          <Row justify="start">
            <Col span={2}>
              <Button
                style={{
                  color: '#3c6ef0',
                  border: 'none',
                  fontWeight: '700',
                }}
                onClick={() => onSelect(StopPointType.HOME)}
              >
                {info.homeList?.length > 0 ? '更换' : '添加'}
              </Button>
            </Col>
            <Col span={5}>
              {info.homeList?.length > 0
                ? info.homeList.map((item: any) => {
                    return (
                      <StopPointNameTag
                        name={item.name}
                        waitingTime={item.waitingTime}
                        id={item.id}
                        key={item.id}
                      />
                    );
                  })
                : ''}
            </Col>
          </Row>
        </Form.Item>
        <Form.Item label="取货点">
          <Button
            style={{
              color: '#3c6ef0',
              border: 'none',
              fontWeight: '700',
              marginBottom: '10px',
            }}
            onClick={() => onSelect(StopPointType.PICKUP)}
          >
            {info.stopList?.length > 0 ? '更换' : '添加'}
          </Button>
          <Row justify="start" gutter={[8, 8]}>
            {info.stopList?.length > 0
              ? info.stopList.map((item: any) => {
                  return (
                    <Col span={4} key={item.id}>
                      <StopPointNameTag
                        name={item.name}
                        waitingTime={item.waitingTime}
                        id={item.id}
                      />
                    </Col>
                  );
                })
              : ''}
          </Row>
        </Form.Item>
        <Form.Item
          label=" "
          labelCol={{ span: 23 }}
          wrapperCol={{ span: 1 }}
          colon={false}
          name="delete"
        >
          <Popconfirm
            title="确定去掉该栏吗？"
            onConfirm={() => {
              crossStopInfo.forEach((item: any, index: any) => {
                if (item.sort === info.sort) {
                  crossStopInfo.splice(index, 1);
                }
              });
              saveInfo([...crossStopInfo]);
            }}
            // onCancel={cancel}
            okText="确定"
            cancelText="取消"
          >
            <DeleteOutlined style={{ color: '#3c6ef0', fontSize: '20px' }} />
          </Popconfirm>
        </Form.Item>
      </Form>
      <Modal
        className="cross-stop-modal"
        width={1000}
        open={selectHomeModal}
        onCancel={() => {
          setSelectHomeModal(false);
          setHomeModalTitle(null);
          setAllPoint([]);
          setSelectHomeRows([]);
          setSelectedHomeRowKeys([]);
        }}
        onOk={onSubmitSelectedHomePoint}
        title={`${homeModalTitle}更新home点停靠点`}
      >
        <Form
          form={homePointForm}
          wrapperCol={{ span: 20 }}
          labelCol={{ span: 3 }}
        >
          <Form.Item label="当前home点">
            <div>
              {info.homeList?.length > 0
                ? `${info.homeList[0].name} ${info.homeList[0].waitingTime}分钟`
                : '-'}
            </div>
          </Form.Item>
          <Form.Item label="home点更换为">
            <Table
              rowKey={(record: any) => record.id}
              columns={columns}
              dataSource={allPoint}
              pagination={false}
              bordered
              rowSelection={{
                columnTitle: <div>单选</div>,
                columnWidth: '50px',
                type: 'radio',
                selectedRowKeys: selectedHomeRowKeys,
                onChange: (selectedRowKeys: any, selectedRows: any) => {
                  setSelectedHomeRowKeys(selectedRowKeys);
                  setSelectHomeRows(selectedRows);
                },
              }}
            />
          </Form.Item>
        </Form>
      </Modal>
      <Modal
        className="cross-stop-modal"
        width={1200}
        open={selectStopModal}
        onCancel={() => {
          setSelectStopModal(false);
          setStopModalTitle(null);
          setAllPoint([]);
          setTargetKeys(
            info.stopList.length > 0
              ? info.stopList.map((item: any) => item.id)
              : [],
          );
        }}
        onOk={onSubmitSelectedStopPoint}
        title={stopModalTitle}
      >
        <TransferWithTable
          leftColumns={columns}
          rightColumns={columns}
          onSelectChange={(list: any) => setTargetKeys(list)}
          titles={[
            <div className="transfer-title" key={'left'}>
              可选停靠点
            </div>,
            <div className="transfer-title" key={'right'}>
              已选停靠点
            </div>,
          ]}
          dataSource={allPoint}
          targetKeys={targetKeys}
          showSearch={true}
          showSelectAll={false}
          tableSelectAll={true}
          scrollY={350}
          leftTableLoading={stopPointTableLoading}
          rowKey={'id'}
          searchColumn={'name'}
        />
      </Modal>
    </div>
  );
};

export default React.memo(CrossStopCard);
