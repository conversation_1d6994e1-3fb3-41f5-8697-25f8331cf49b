import { FormConfig } from '@jd/x-coreui';

// 表格列配置
export const tableColumns: any[] = [
  {
    title: '省市区',
    dataIndex: 'countryName',
    align: 'left',
    width: 150,
    fixed: 'left',
  },
  {
    title: '省区片区',
    dataIndex: 'areaName',
    align: 'left',
    width: 120,
    fixed: 'left',
  },
  {
    title: '站点名称',
    dataIndex: 'stationName',
    align: 'left',
    width: 120,
    fixed: 'left',
  },
  {
    title: '站点编号',
    dataIndex: 'stationNumber',
    align: 'left',
    width: 120,
    fixed: 'left',
  },
  {
    title: '需求编码',
    dataIndex: 'requirementNumber',
    align: 'left',
    width: 120,
  },
  {
    title: '详细地址',
    dataIndex: 'address',
    align: 'left',
    width: 200,
  },
  {
    title: '站点用途',
    dataIndex: 'stationUseCaseName',
    align: 'left',
    width: 100,
  },
  {
    title: '车辆型号',
    dataIndex: 'vehicleModelName',
    align: 'left',
    width: 120,
  },
  {
    title: '车辆数',
    dataIndex: 'count',
    align: 'left',
    width: 80,
  },
  {
    title: '提报人',
    dataIndex: 'contact',
    align: 'left',
    width: 100,
  },
  {
    title: '联系方式',
    dataIndex: 'contactPhone',
    align: 'left',
    width: 120,
  },
  {
    title: '备选联系人',
    dataIndex: 'alternateContact',
    align: 'left',
    width: 120,
  },
  {
    title: '备选联系方式',
    dataIndex: 'alternateContactPhone',
    align: 'left',
    width: 120,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    align: 'left',
    width: 150,
  },
  {
    title: '审批人',
    dataIndex: 'reviewerUser',
    align: 'left',
    width: 100,
  },
  {
    title: '更新时间',
    dataIndex: 'statusModifyTime',
    align: 'left',
    width: 150,
  },
  {
    title: '审核状态',
    dataIndex: 'statusName',
    align: 'left',
    width: 100,
    fixed: 'right',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 100,
    align: 'left',
    fixed: 'right',
  },
];

// 搜索表单配置
export const searchConfig: FormConfig = {
  fields: [
    {
      fieldName: 'provinceCityCountry',
      label: '省市区',
      placeholder: '请选择省市区',
      type: 'cascader',
      mapRelation: { label: 'name', value: 'id', children: 'children' },
    },
    {
      fieldName: 'provinceAgencyArea',
      label: '省区片区',
      placeholder: '请选择省区片区',
      type: 'cascader',
      mapRelation: { label: 'name', value: 'code', children: 'children' },
    },
    {
      fieldName: 'stationNumber',
      label: '站点名称',
      placeholder: '请选择站点',
      type: 'select',
      showSearch: true,
      labelInValue: false,
      mapRelation: { label: 'stationName', value: 'stationNumber' },
    },
    {
      fieldName: 'vehicleModelType',
      label: '车辆型号',
      placeholder: '请选择车辆型号',
      type: 'select',
      showSearch: true,
      labelInValue: false,
      mapRelation: { label: 'vehicleModelName', value: 'vehicleModelType' },
    },
    {
      fieldName: 'stationUseCase',
      label: '站点用途',
      placeholder: '请选择站点用途',
      type: 'select',
      showSearch: true,
      labelInValue: false,
      mapRelation: { label: 'name', value: 'code' },
    },
    {
      fieldName: 'createTime',
      label: '创建时间',
      type: 'rangeTime',
      picker: 'date',
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      xxl: 8,
      xl: 8,
      lg: 12,
    },
  ],
};

export enum AuditStatus {
  TOTAL = 'TOTAL',
  REVIEW = 'REVIEW',
  PLACED = 'PLACED',
  REJECTED = 'REJECTED',
}

// Tab配置
export const tabsConfig = [
  {
    key: '',
    label: '全部',
    statusKey: AuditStatus.TOTAL,
  },
  {
    key: 'REVIEW',
    label: '审批中',
    statusKey: AuditStatus.REVIEW,
  },
  {
    key: 'PLACED',
    label: '已下单',
    statusKey: AuditStatus.PLACED,
  },
  {
    key: 'REJECTED',
    label: '已驳回',
    statusKey: AuditStatus.REJECTED,
  },
];

export const statusNameStyle = {
  [AuditStatus.REVIEW]: {
    textColor: 'rgba(128, 161, 245, 1)',
    bgColor: 'rgba(128, 161, 245, 0.1)',
  },
  [AuditStatus.PLACED]: {
    textColor: 'rgba(26, 181, 98, 1)',
    bgColor: 'rgba(26, 181, 98, 0.1)',
  },
  [AuditStatus.REJECTED]: {
    textColor: 'rgba(252, 61, 61, 1)',
    bgColor: 'rgba(252, 61, 61, 0.1)',
  },
};

// 默认隐藏的列
export const defaultHiddenColumns = [
  'countryName',
  'areaName',
  'stationNumber',
];

// 默认固定在左侧的列
export const defaultLeftFixedColumns = ['stationName'];

// 默认固定在右侧的列
export const defaultRightFixedColumns = ['statusName', 'operation'];

// 详情页面表单配置
export const detailFormConfig: FormConfig = {
  fields: [
    {
      fieldName: 'contact',
      label: '联系人',
      type: 'input',
      placeholder: '请输入联系人',
      validatorRules: [
        {
          required: true,
          message: '请输入联系人',
        },
      ],
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'contactPhone',
      label: '联系电话',
      type: 'input',
      placeholder: '请输入联系电话',
      validatorRules: [
        {
          required: true,
          message: '请输入联系电话',
        },
        {
          pattern: /^1[3-9]\d{9}$/,
          message: '请输入正确的手机号码',
        },
      ],
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'mail',
      label: '电子邮箱',
      type: 'input',
      placeholder: '请输入电子邮箱',
      validatorRules: [
        {
          required: true,
          message: '请输入电子邮箱',
        },
        {
          type: 'email',
          message: '请输入正确的邮箱格式',
        },
      ],
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'stationNumber',
      label: '站点编号',
      type: 'input',
      placeholder: '请输入站点编号',
      validatorRules: [
        {
          required: true,
          message: '请输入站点编号',
        },
      ],
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'stationName',
      label: '所属站点',
      type: 'input',
      placeholder: '请输入所属站点',
      validatorRules: [
        {
          required: true,
          message: '请输入所属站点',
        },
      ],
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'stationUseCase',
      label: '站点用途',
      type: 'select',
      placeholder: '请选择站点用途',
      validatorRules: [
        {
          required: true,
          message: '请选择站点用途',
        },
      ],
      labelInValue: false,
      mapRelation: { label: 'name', value: 'code' },
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'count',
      label: '用车数量',
      type: 'inputNumber',
      placeholder: '请输入用车数量',
      validatorRules: [
        {
          required: true,
          message: '请输入用车数量',
        },
        {
          type: 'number',
          min: 1,
          message: '用车数量必须大于0',
        },
      ],
      min: 1,
      precision: 0,
      step: 1,
      controls: true,
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'provinceCityCountry',
      label: '配送省市',
      type: 'cascader',
      placeholder: '请选择配送省市',
      validatorRules: [
        {
          required: true,
          message: '请选择配送省市',
        },
      ],
      mapRelation: { label: 'name', value: 'id', children: 'children' },
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'address',
      label: '详细地址',
      type: 'input',
      placeholder: '请输入详细地址',
      validatorRules: [
        {
          required: true,
          message: '请输入详细地址',
        },
      ],
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'expectedDeliveryMonth',
      label: '预期交付月份',
      type: 'datePicker',
      placeholder: '请选择预期交付月份',
      validatorRules: [
        {
          required: true,
          message: '请选择预期交付月份',
        },
      ],
      picker: 'month',
      format: 'YYYY-MM',
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
  ],
};

// 车辆型号数据
export const vehicleModelList = [
  {
    name: 'JD-6EF型配送车',
    url: require('@/assets/image/deployProcess/jd-6ef.png'),
    count: 0,
    isActive: false,
    vehicleModelType: 'JD_6EF',
  },
  {
    name: 'WYZX-W5型配送车',
    url: require('@/assets/image/deployProcess/wyzx-w5.png'),
    count: 0,
    isActive: false,
    vehicleModelType: 'WYZX_W5',
  },
  {
    name: 'XSQ-X3型配送车',
    url: require('@/assets/image/deployProcess/xsq-x3.png'),
    count: 0,
    isActive: false,
    vehicleModelType: 'XSQ_X3',
  },
  {
    name: 'XSQ-X6型配送车',
    url: require('@/assets/image/deployProcess/xsq-x6.png'),
    count: 0,
    isActive: false,
    vehicleModelType: 'XSQ_X6',
  },
];

// 修改记录弹窗表格列配置
export const modifyLogTableColumns = [
  {
    title: '修改人',
    dataIndex: 'userName',
    align: 'left' as const,
    width: 120,
    render: (text: any) => text || '-',
  },
  {
    title: '修改时间',
    dataIndex: 'modifyTime',
    align: 'left' as const,
    width: 180,
    render: (text: any) => text || '-',
  },
  {
    title: '修改字段',
    dataIndex: 'fieldName',
    align: 'left' as const,
    width: 120,
    render: (text: any) => text || '-',
  },
  {
    title: '修改前',
    dataIndex: 'beforeValue',
    align: 'left' as const,
    width: 200,
    render: (text: any) => text || '-',
  },
  {
    title: '修改后',
    dataIndex: 'afterValue',
    align: 'left' as const,
    width: 200,
    render: (text: any) => text || '-',
  },
];
