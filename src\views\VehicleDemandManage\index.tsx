import React, { useState, useEffect, useRef, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '@/redux/store';
import { message, Modal } from 'antd';
import {
  CommonTable,
  CommonForm,
  ExcelUploader,
  useTableData,
} from '@jd/x-coreui';
import CustomTabs from '@/components/CustomTabs';
import showModal from '@/components/CommonModal';
import OrderConfirmModal from './components/OrderConfirmModal/OrderConfirmModal';
import {
  searchConfig,
  tableColumns,
  tabsConfig,
  defaultHiddenColumns,
  defaultLeftFixedColumns,
  defaultRightFixedColumns,
  statusNameStyle,
} from './utils/constant';
import { vehicleDemandManageApi, commonApi } from '@/fetch/business';
import { useCommonDropDown } from '@/utils/hooks';
import { HttpStatusCode } from '@/fetch/core/constant';
import {
  sortColumnsByState,
  createDefaultColumnsState,
} from '@jd/x-coreui/lib/components/CommonTable/columnUtils';
import { saveSearchValues } from '@/redux/reducer/searchForm';
import { formatDateToSecond } from '@/utils/utils';
import './index.scss';

const initSearchCondition = {
  // 表单组件所需特殊数据结构的字段
  provinceCityCountry: null,
  provinceAgencyArea: null,
  createTime: null,
  // 列表接口入参字段
  searchForm: {
    stationNumber: null,
    vehicleModelType: null,
    stationUseCase: null,
    startTime: '',
    endTime: '',
    provinceId: null,
    cityId: null,
    countryId: null,
    provinceAgencyCode: '',
    areaCode: '',
    status: '',
  },
  // 页码字段
  pageNum: 1,
  pageSize: 10,
};
// 下拉框数据映射
const dropdownDataMap = {
  provinceCityCountry: {
    fetchApi: commonApi.getProvinceCityCountryList(),
    options: [],
  },
  provinceAgencyArea: {
    fetchApi: commonApi.getProvinceAgencyAreaList(),
    options: [],
  },
  stationNumber: {
    fetchApi: commonApi.getStationList(),
    options: [],
  },
  vehicleModelType: {
    fetchApi: commonApi.getVehicleModelList(),
    options: [],
  },
};
const VehicleDemandManage = () => {
  const dispatch = useDispatch();
  const historySearchValues = useSelector(
    (state: RootState) => state.searchForm,
  );
  const navigator = useNavigate();
  const searchFormRef = useRef<any>(null);
  const searchRef = useRef<any>(null);

  const [searchCondition, setSearchCondition] = useState(() => {
    console.log('有历史数据吗', historySearchValues);
    return historySearchValues.searchValues
      ? historySearchValues.searchValues
      : initSearchCondition;
  });
  const [activeTabKey, setActiveTabKey] = useState<string>(() => {
    return historySearchValues.activeTabKey
      ? historySearchValues.activeTabKey
      : '';
  });
  const [countMap, setCountMap] = useState({
    TOTAL: 0,
    REVIEW: 0,
    PLACED: 0,
    REJECTED: 0,
  });
  const [selectInfo, setSelectInfo] = useState<{
    selectedRowKeys: any[];
    selectedRows: any[];
    clearFunc: any;
  }>({
    selectedRowKeys: [],
    selectedRows: [],
    clearFunc: () => {},
  });
  const [searchFormConfig, setSearchFormConfig] = useState(searchConfig);
  const [uploadVisible, setUploadVisible] = useState(false);
  const [downloadUrl, setDownloadUrl] = useState('');
  const [uploadFileKey, setUploadFileKey] = useState('');

  const defaultColumnsState = useMemo(() => {
    return createDefaultColumnsState(
      tableColumns,
      defaultHiddenColumns,
      defaultLeftFixedColumns,
      defaultRightFixedColumns,
    );
  }, []);
  const [columnsState, setColumnsState] = useState<any>(defaultColumnsState);
  const { tableData, loading } = useTableData(
    {
      ...searchCondition.searchForm,
      pageNum: searchCondition.pageNum,
      pageSize: searchCondition.pageSize,
    },
    vehicleDemandManageApi.getVehicleDemandPage,
  );
  const dropDownData = useCommonDropDown(['STATION_USE_CASE']);

  useEffect(() => {
    if (dropDownData?.stationUseCaseList?.length > 0) {
      initializeDropdownData();
    }
  }, [dropDownData]);

  useEffect(() => {
    if ((tableData as any)?.countMap) {
      setCountMap((tableData as any).countMap);
    }
  }, [tableData]);

  const initializeDropdownData = async () => {
    try {
      const allApi = Object.values(dropdownDataMap).map(
        (item) => item.fetchApi,
      );
      const allRes = await Promise.all(allApi);
      const dropdownKeys = Object.keys(dropdownDataMap);
      allRes?.forEach((res, index) => {
        if (res && res.code === HttpStatusCode.Success && res.data) {
          const currentKey = dropdownKeys[index];
          dropdownDataMap[currentKey].options = res.data;
        }
      });
      const newSearchConfig = {
        ...searchConfig,
        fields: searchConfig.fields.map((field) => {
          switch (field.fieldName) {
            case 'provinceCityCountry':
              return {
                ...field,
                options: dropdownDataMap.provinceCityCountry.options,
              };
            case 'provinceAgencyArea':
              return {
                ...field,
                options: dropdownDataMap.provinceAgencyArea.options,
              };
            case 'stationNumber':
              return {
                ...field,
                options: dropdownDataMap.stationNumber.options,
              };
            case 'vehicleModelType':
              return {
                ...field,
                options: dropdownDataMap.vehicleModelType.options,
              };
            case 'stationUseCase':
              return {
                ...field,
                options: dropDownData.stationUseCaseList,
              };
            default:
              return field;
          }
        }),
      };
      setSearchFormConfig(newSearchConfig);
    } catch (error) {
      console.error('初始化下拉框数据失败:', error);
      message.error('初始化下拉框数据失败');
    }
  };

  const onSearchClick = (values: any) => {
    const newCondition = formatSearchValues(values, { pageNum: 1 });
    setSearchCondition(newCondition);
    saveHistorySearchValue(newCondition);
  };

  const onResetClick = () => {
    const resetValues = {
      ...initSearchCondition,
      searchForm: {
        ...initSearchCondition.searchForm,
        status: activeTabKey || '',
      },
    };
    setSearchCondition(resetValues);
    saveHistorySearchValue(resetValues);
    if (searchFormRef.current) {
      searchFormRef.current.resetFields();
    }
  };

  const formatSearchValues = (values: any, currentData: any) => {
    const {
      createTime,
      provinceCityCountry,
      provinceAgencyArea,
      ...otherValues
    } = values;
    const { status, pageNum, pageSize } = currentData;
    const formatTime: any =
      createTime?.length > 0 ? formatDateToSecond(createTime) : {};
    const newSearchCondition = {
      createTime,
      provinceCityCountry,
      provinceAgencyArea,
      searchForm: {
        ...otherValues,
        startTime: formatTime.startTime
          ? `${formatTime.startTime.split(' ')[0]} 00:00:00`
          : '',
        endTime: formatTime.endTime
          ? `${formatTime.endTime.split(' ')[0]} 23:59:59`
          : '',
        provinceId: provinceCityCountry?.[0] || null,
        cityId: provinceCityCountry?.[1] || null,
        countryId: provinceCityCountry?.[2] || null,
        provinceAgencyCode: provinceAgencyArea?.[0] || '',
        areaCode: provinceAgencyArea?.[1] || '',
        status: status === undefined || status === null ? activeTabKey : status,
      },
      pageNum:
        pageNum === undefined || pageNum === null
          ? searchCondition.searchForm.pageNum
          : pageNum,
      pageSize:
        pageSize === undefined || pageSize === null
          ? searchCondition.searchForm.pageSize
          : pageSize,
    };
    return newSearchCondition;
  };

  const handleTabChange = (key: string) => {
    setActiveTabKey(key);
    const currentSearchForm = searchFormRef.current?.getFieldsValue() || {};
    const newCondition = formatSearchValues(currentSearchForm, {
      pageNum: 1,
      status: key,
    });
    setSearchCondition(newCondition);
    saveHistorySearchValue(newCondition, key);
    selectInfo.clearFunc();
    setSelectInfo({
      selectedRowKeys: [],
      selectedRows: [],
      clearFunc: () => {},
    });
  };

  const formatColumns = useMemo(() => {
    return tableColumns.map((col) => {
      switch (col?.dataIndex) {
        case 'operation':
          return {
            ...col,
            render: (_: any, record: any) => {
              return (
                <div className="operate">
                  <a
                    onClick={() => {
                      navigator(
                        `/app/vehicleDemandManage/detail?type=READONLY&id=${record?.id}&status=${record?.status}`,
                      );
                    }}
                  >
                    查看详情
                  </a>
                </div>
              );
            },
          };
        case 'statusName':
          return {
            ...col,
            render: (text: any, record: any) => {
              const textColor =
                statusNameStyle[record?.status]?.textColor ||
                'rgba(0, 0, 0, 0.88)';
              const bgColor =
                statusNameStyle[record?.status]?.bgColor || '#fff';
              return text ? (
                <div
                  style={{
                    color: textColor,
                    backgroundColor: bgColor,
                    borderRadius: '5px',
                    width: 'fit-content',
                    padding: '3px',
                  }}
                >
                  {text}
                </div>
              ) : (
                '-'
              );
            },
          };
        default:
          return {
            ...col,
            render: (text: any) => `${text ?? '-'}`,
          };
      }
    });
  }, []);

  const dynamicColumns = useMemo(() => {
    return sortColumnsByState(formatColumns, columnsState) || [];
  }, [formatColumns, columnsState]);

  const saveHistorySearchValue = (
    searchValues: any,
    currentTabKey?: string,
  ) => {
    dispatch(
      saveSearchValues({
        routeName: location.pathname,
        searchValues,
        activeTabKey:
          currentTabKey === undefined || currentTabKey === null
            ? activeTabKey
            : currentTabKey,
      }),
    );
  };

  // 批量下单处理函数
  const handleBatchOrder = () => {
    if (selectInfo.selectedRowKeys?.length === 0) {
      message.error('请至少选择一条数据');
      return;
    }
    const orderList = selectInfo.selectedRows.map((row: any) => ({
      requirementId: row.id,
      vehicleModelType: row.vehicleModelType || 'UNKNOWN',
      vehicleModelName: row.vehicleModelName || '未知车型',
      count: row.count || 0,
      stationName: row.stationName || '',
      contact: row.contact || '',
      contactPhone: row.contactPhone || '',
      stationNumber: row.stationNumber || '',
      address: row.address || '',
    }));

    let orderConfirmRef: any = null;

    showModal({
      title: '批量下单确认',
      width: 800,
      content: (
        <OrderConfirmModal
          orderList={orderList}
          onConfirm={(orderData) => {
            handleBatchOrderConfirm(orderData);
          }}
          ref={(ref) => (orderConfirmRef = ref)}
        />
      ),
      footer: {
        showOk: true,
        showCancel: true,
        okText: '确定下单',
        cancelText: '取消',
        okFunc: async (closeModal) => {
          try {
            if (orderConfirmRef?.handleSubmit) {
              await orderConfirmRef.handleSubmit();
              closeModal();
            }
          } catch (error) {
            console.error('批量下单验证失败:', error);
          }
        },
        cancelFunc: (closeModal) => {
          closeModal();
        },
      },
    });
  };

  const handleBatchOrderConfirm = async (orderData: any[]) => {
    try {
      const response = await vehicleDemandManageApi.submitOrder({
        submitOrderList: orderData,
      });
      if (response.code === HttpStatusCode.Success) {
        message.success('批量下单成功');
        selectInfo.clearFunc();
        setSearchCondition({ ...searchCondition });
      } else {
        message.error(response.message || '批量下单失败');
      }
    } catch (error) {
      console.error('批量下单失败:', error);
      message.error('批量下单失败');
    }
  };

  // 批量驳回处理函数
  const handleBatchReject = () => {
    if (selectInfo.selectedRowKeys?.length === 0) {
      message.error('请至少选择一条数据');
      return;
    }

    Modal.confirm({
      title: '确认批量驳回',
      content: `确定要驳回选中的 ${selectInfo.selectedRowKeys.length} 条需求吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          const response = await vehicleDemandManageApi.rejectRequirement({
            requirementIdList: selectInfo.selectedRowKeys,
          });
          if (response.code === HttpStatusCode.Success) {
            message.success('批量驳回成功');
            selectInfo.clearFunc();
            setSearchCondition({ ...searchCondition });
          } else {
            message.error(response.message || '批量驳回失败');
          }
        } catch (error) {
          console.error('批量驳回失败:', error);
          message.error('批量驳回失败');
        }
      },
    });
  };

  // 批量上传处理函数
  const handleBatchUpload = async () => {
    try {
      const downloadResponse = await vehicleDemandManageApi.getDownloadUrl({
        type: 'DEPLOYMENT_REQUIREMENT',
      });
      const url =
        downloadResponse.code === HttpStatusCode.Success
          ? downloadResponse.data?.url
          : '';
      setDownloadUrl(url);
      setUploadVisible(true);
    } catch (error) {
      console.error('获取模板下载链接失败:', error);
      message.error('获取模板下载链接失败');
    }
  };

  const handleUploadCancel = () => {
    setUploadVisible(false);
  };

  const handleUploadConfirm = async () => {
    if (!uploadFileKey) {
      message.error('请等待，上传中');
      return;
    }
    try {
      const response = await vehicleDemandManageApi.batchSubmit({
        fileKey: uploadFileKey,
      });
      if (response.code === HttpStatusCode.Success) {
        message.success('批量上传成功');
        setUploadVisible(false);
        selectInfo.clearFunc();
        setSearchCondition({ ...searchCondition });
      } else {
        message.error(response.message || '批量上传失败');
      }
    } catch (error) {
      console.error('批量上传失败:', error);
      message.error('批量上传失败');
    }
  };

  const middleBtns: any[] = [
    {
      show: true,
      title: '新增用车需求',
      key: 'addDemand',
      onClick: () => {
        console.log('新增需求');
        navigator('/app/vehicleDemandManage/detail?type=ADD');
      },
    },
    {
      show: true,
      title: '批量下单',
      key: 'batchOrder',
      onClick: handleBatchOrder,
    },
    {
      show: true,
      title: '批量驳回',
      key: 'batchReject',
      onClick: handleBatchReject,
    },
    {
      show: true,
      title: '批量上传用车需求',
      key: 'batchUpload',
      onClick: handleBatchUpload,
    },
  ];

  const formatTabsConfig = useMemo(() => {
    return tabsConfig.map((tab) => ({
      key: tab.key,
      label: tab.label,
      count: countMap?.[tab.statusKey as keyof typeof countMap] || 0,
    }));
  }, [countMap]);

  return (
    <div className="vehicle-demand-manage">
      <div ref={searchRef}>
        <CommonForm
          formConfig={searchFormConfig}
          defaultValue={{
            ...searchCondition,
            ...searchCondition.searchForm,
          }}
          layout="inline"
          formType="search"
          getFormInstance={(ref) => (searchFormRef.current = ref)}
          onSearchClick={onSearchClick}
          onResetClick={onResetClick}
        />
      </div>
      <div className="table-container">
        <CustomTabs
          items={formatTabsConfig}
          activeKey={activeTabKey}
          onChange={handleTabChange}
        />
        <CommonTable
          tableListData={{
            list: (tableData as any)?.list ?? [],
            totalNumber: (tableData as any)?.total,
            totalPage: (tableData as any)?.pages,
          }}
          tableKey={'vehicle-demand-manage-table'}
          columns={dynamicColumns}
          loading={loading}
          rowKey="id"
          middleBtns={middleBtns}
          searchCondition={searchCondition}
          onPageChange={(value: any) => {
            console.log('此时的分页变化', value);
            saveHistorySearchValue(value);
            setSearchCondition(value);
          }}
          crossPageSelect={(keys: any, rows: any, clearFunc: any) => {
            setSelectInfo({
              selectedRowKeys: keys,
              selectedRows: rows,
              clearFunc: clearFunc,
            });
          }}
          // 列配置相关属性
          showColumnSetting={true}
          columnsState={{
            value: columnsState,
            onChange: setColumnsState,
            persistenceType: 'localStorage',
          }}
          defaultColumnsState={defaultColumnsState}
          searchRef={searchRef}
        />
        <ExcelUploader
          downLoadTemplate={() => {
            window.open(downloadUrl, '_self');
          }}
          visible={uploadVisible}
          onCancel={handleUploadCancel}
          title="批量上传用车需求"
          description={null}
          uploadToS3={true}
          uploadMode="dragger"
          showStepGuidance={true}
          downloadIconText=""
          s3Config={{
            getPreSignatureUrl: `${location.protocol}//${process.env.JDX_APP_CLOUD_FETCH_DOMAIN}/infrastructure/oss/getPreUrl`,
            bucketName: 'rover-operation',
            LOPDN: process.env.JDX_APP_REQUEST_HEADER!,
            maxFileSize: 50,
            unit: 'MB',
            needMd5Validete: false,
            onS3Success: (fileKey: string) => {
              setUploadFileKey(fileKey);
              console.log('文件上传成功:', fileKey);
            },
            onS3Error: (error: Error) => {
              console.error('文件上传失败:', error);
              message.error('文件上传失败');
            },
          }}
          onS3UploadConfirm={handleUploadConfirm}
        />
      </div>
    </div>
  );
};

export default React.memo(VehicleDemandManage);
