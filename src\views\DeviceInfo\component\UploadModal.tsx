/* eslint-disable react/no-unescaped-entities */
/* eslint-disable no-unused-vars */

import { Col, Form, Input, message, Modal, Row } from 'antd';
import React, { useEffect, useState } from 'react';
import { CustomButton, ButtonType } from '@/components';
import { CommonApi, downLoadUrlType } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';
import { FileUpload } from '@jd/x-coreui';
import { request } from '@/fetch/core';

const modalConfig: any = {
  addVehicle: {
    type: downLoadUrlType.VEHICLE,
    modalTitle: '批量新增车辆',
    downloadBtnTitle: '批量新增车辆模板',
    downloadPath: '',
    uploadPath: '',
    tips: (
      <>
        <div>
          1、导入模板"<b style={{ color: 'red' }}>*</b>"为必填项；
        </div>
        <div>2、非必填项系统不校验准确性。</div>
      </>
    ),
  },
  addCardNo: {
    type: downLoadUrlType.CARD,
    modalTitle: '批量导入更多设备号',
    downloadBtnTitle: '导入模板',
    downloadPath: '',
    uploadPath: '',
    tips: (
      <>
        <div>
          1、导入模板"<b style={{ color: 'red' }}>*</b>"为必填项；
        </div>
        <div>2、系统不校验非必填内容。</div>
      </>
    ),
  },
};

const UploadModal = ({
  visable,
  module,
  onComplete,
}: {
  visable: boolean;
  module: 'addVehicle' | 'addCardNo';
  onComplete: Function;
}) => {
  const commonFetchApi = new CommonApi();
  const [editForm] = Form.useForm();
  const basicConfig = modalConfig[module];
  const [uploadStatus, setUploadStatus] = useState<
    'uploading' | 'finished' | 'normal'
  >('normal');
  const [fileKey, setFileKey] = useState<string | null>(null);
  const [uploadResult, setUploadResult] = useState<{
    id: any;
    name: any;
    totalCount: any;
    successCount: any;
    failCount: any;
    resultDescription: any;
    enable: any;
    buttonStyle: React.CSSProperties;
    downloadUrl: any;
  }>({
    id: null,
    name: null,
    totalCount: 0,
    successCount: 0,
    failCount: 0,
    resultDescription: null,
    enable: 0,
    buttonStyle: {
      color: '#666',
      backgroundColor: 'white',
    },
    downloadUrl: null,
  });
  const [downloadUrl, setDownloadUrl] = useState<any>();

  useEffect(() => {
    getDownloadTemplate();
  }, []);

  const uploadClick = async () => {
    if (!fileKey) {
      message.error('请先选择要上传的文件');
      return;
    }

    setUploadStatus('uploading');
    let reqType = '';
    if (module === 'addCardNo') {
      reqType = 'VEHICLE_CARD_NO_BATCH_ADD';
    } else if (module === 'addVehicle') {
      reqType = 'VEHICLE_BATCH_ADD';
    }

    const resp: any = await commonFetchApi.upload(reqType, {
      bucketName: 'rover-operation',
      fileKey: fileKey,
    });
    if (resp.code === HttpStatusCode.Success) {
      setUploadStatus('finished');
      const { failCount, enable } = resp.data;
      let downloadUrl;
      let buttonStyle: React.CSSProperties = {};
      if (failCount && failCount > 0 && enable === 1) {
        buttonStyle = {
          color: 'white',
          backgroundColor: '#31C2A6',
        };
        // 保存ID和success标志，而不是直接构建URL
        downloadUrl = {
          id: resp.data.id,
          success: resp.data.failCount > 0 ? 0 : 1,
        };
      } else {
        buttonStyle = {
          color: '#333',
          backgroundColor: 'white',
        };
      }
      setUploadResult({
        ...uploadResult,
        ...resp.data,
        buttonStyle,
        downloadUrl,
      });
    } else {
      setUploadStatus('normal');
      message.error(resp.message);
    }
  };

  // 获取批量新增车辆模板及导入更多设备号模板
  const getDownloadTemplate = async () => {
    const res = await commonFetchApi.getDownloadURL(basicConfig.type);
    if (res.code === HttpStatusCode.Success) {
      const newUrl = res.data.url.replace('http', 'https');
      setDownloadUrl(newUrl);
    }
  };

  // 获取上传结果下载URL
  const getUploadResultDownloadUrl = async (id: any, success: number) => {
    try {
      const result = await request({
        method: 'POST',
        path: '/k2/management/upload/upload_result_down',
        body: {
          id,
          success,
        },
      });
      if (
        result.code === HttpStatusCode.Success &&
        result.data &&
        result.data.url
      ) {
        // 将 http URL 替换为 https
        const secureUrl = result.data.url.replace('http:', 'https:');
        window.open(secureUrl, '_self');
      }
    } catch (error) {
      console.error('获取下载URL失败', error);
    }
  };

  const makeUploadContent = () => {
    return (
      <Form form={editForm}>
        <Row>
          <Col span={24}>
            <Form.Item label="下载模板">
              <CustomButton
                title={basicConfig.downloadBtnTitle}
                buttonType={ButtonType.DefaultButton}
                otherCSSProperties={{
                  color: '#1890ff',
                  fontSize: 11,
                  height: 30,
                }}
                onSubmitClick={() => {
                  // 使用 window.open 而不是 window.location.href
                  window.open(downloadUrl);
                }}
              />
            </Form.Item>
          </Col>
        </Row>
        <Row>
          <Col span={24}>
            <Form.Item name="uploadFile" label="上传文件">
              <FileUpload
                fileListType="file"
                accept=".xlsx,.xls"
                getPreSignatureUrl={`${location.protocol}//${process.env.JDX_APP_CLOUD_FETCH_DOMAIN}/infrastructure/oss/getPreUrl`}
                LOPDN={process.env.JDX_APP_REQUEST_HEADER!}
                needMd5Validete={false}
                maxCount={1}
                bucketName="rover-operation"
                onEnd={(uploadedFileKey) => {
                  setFileKey(uploadedFileKey);
                  editForm.setFieldsValue({
                    uploadFile: [uploadedFileKey],
                  });
                }}
                onDelete={() => {
                  setFileKey(null);
                  editForm.setFieldsValue({
                    uploadFile: [],
                  });
                }}
              />
            </Form.Item>
          </Col>
        </Row>
        <Row>
          <Col span={18}>
            <Form.Item label={`\xa0\xa0\xa0\xa0\xa0\xa0说明`}>
              <div style={{ color: '#808080' }}>
                <br />
                {basicConfig.tips}
              </div>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    );
  };

  const makeUploadFinishedContent = () => {
    return (
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          fontSize: '15px',
        }}
      >
        <div>{uploadResult.resultDescription ?? ''}</div>
        <div
          style={{
            marginTop: 20,
          }}
        >
          <CustomButton
            title="下载错误列表"
            buttonType={
              uploadResult.failCount > 0 && uploadResult.enable === 1
                ? ButtonType.PrimaryButton
                : ButtonType.DefaultButton
            }
            onSubmitClick={() => {
              if (uploadResult.failCount > 0 && uploadResult.enable === 1) {
                // 使用新的方法获取下载URL
                if (
                  uploadResult.downloadUrl &&
                  typeof uploadResult.downloadUrl === 'object'
                ) {
                  getUploadResultDownloadUrl(
                    uploadResult.downloadUrl.id,
                    uploadResult.downloadUrl.success,
                  );
                }
              }
            }}
          />
        </div>
      </div>
    );
  };

  return (
    <Modal
      width="50vw"
      maskClosable={false}
      title={
        uploadStatus === 'finished'
          ? `【${basicConfig.modalTitle}】导入结果`
          : basicConfig.modalTitle
      }
      footer={uploadStatus === 'finished' ? null : undefined}
      closable={uploadStatus === 'finished'}
      visible={visable}
      okText="上传"
      confirmLoading={uploadStatus === 'uploading'}
      cancelText="取消"
      onOk={uploadClick}
      onCancel={() => {
        onComplete ? onComplete(uploadResult.id !== null) : null;
      }}
    >
      {uploadStatus === 'finished'
        ? makeUploadFinishedContent()
        : makeUploadContent()}
    </Modal>
  );
};

export default React.memo(UploadModal);
