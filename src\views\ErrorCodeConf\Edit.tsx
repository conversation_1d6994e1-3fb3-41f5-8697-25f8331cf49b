import { message, FormInstance } from 'antd';
import React, { useEffect, useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { CommonEdit, CommonForm, FormConfig } from '@/components';
import { ErrorCodeConfApi } from '@/fetch/business';
import { editFormConfig } from './utils/columns';
import { useEditPageData } from '@/utils/hooks';
import { formatLocation } from '@/utils/utils';
import { PageType, ErrorCodeConfTitle } from '@/utils/EditTitle';
import { HttpStatusCode } from '@/fetch/core/constant';
import cloneDeep from 'lodash/cloneDeep';

const ErrorCodeConfEdit = () => {
  const fetchApi = new ErrorCodeConfApi();
  const navigator = useNavigate();
  const formRef = useRef<FormInstance>();
  const { id, type } = formatLocation(window.location.search);
  const [editFormFields, setEditFormFields] =
    useState<FormConfig>(editFormConfig);

  const detailData = useEditPageData(id, fetchApi.fetchDetail);
  const breadCrumbItems = [
    {
      title: '错误信息翻译',
      route: '',
    },
    {
      title: ErrorCodeConfTitle[type],
      route: '',
    },
  ];

  useEffect(() => {
    formateFormConfig();
  }, []);
  const formateFormConfig = async () => {
    const _fields = cloneDeep(editFormConfig.fields);
    setEditFormFields({ ...editFormConfig, fields: _fields });
  };

  const getFormInstance = (val) => {
    formRef.current = val;
  };
  const confirm = async () => {
    try {
      const value = await formRef.current?.validateFields();
      const params = {
        ...detailData,
        ...value,
      };
      const response = await fetchApi.submitInfo({
        type: type === PageType.EDIT ? PageType.EDIT : PageType.ADD,
        requestBody: {
          ...params,
        },
      });
      if (response.code === HttpStatusCode.Success) {
        message.success(type === PageType.EDIT ? '编辑成功' : '创建成功');
        navigator(-1);
      } else {
        message.error(response.message);
      }
    } catch (e) {
      message.error('请填写必填项');
      console.log(e);
    }
  };

  return (
    <CommonEdit
      title={ErrorCodeConfTitle[type]}
      breadCrumbConfig={breadCrumbItems}
      onSubmitClick={confirm}
      onCancleClick={() => {
        navigator(-1);
      }}
    >
      <CommonForm
        formConfig={editFormFields}
        defaultValue={type === PageType.ADD ? { enable: 1 } : detailData}
        getFormInstance={getFormInstance}
      />
    </CommonEdit>
  );
};

export default React.memo(ErrorCodeConfEdit);
