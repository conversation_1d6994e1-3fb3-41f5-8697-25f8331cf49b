/* eslint-disable no-unused-vars */
import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { Col, Row } from 'antd';
import debounce from 'lodash/debounce';
import leftImg from '@/assets/image/grid/left-grid-car.png';
import rightImg from '@/assets/image/grid/right-grid-car.png';
import './index.scss';
import { commonDataSelector } from '@/redux/reducer/commonData';

const smallGridListPosition = {
  top: 103,
  width: 231,
  height: 135,
  // backgroundColor:'lightblue'
};
const bigGridListPosition = {
  top: 131,
  width: 308,
  height: 179,
  // backgroundColor:'lightblue'
};
const Preview = () => {
  const gridList = useSelector(commonDataSelector).gridList;
  const gridPositionValue = useSelector(commonDataSelector).gridPositionValue;
  const refreshPreview = useSelector(commonDataSelector).refreshPreview;

  const [imgStyle, setImgStyle] = useState<any>({});
  const [gridtstyle, setGridtStyle] = useState<any>({}); // 货箱盒子的样式
  const [caseSize, setCaseSize] = useState<any>({}); // 货箱的宽和高
  const [leftColArr, setLeftColArr] = useState<any[]>([]);
  const [rightColArr, setRightColArr] = useState<any[]>([]);
  // 每列的宽度
  const [leftColWeigh, setLeftColWeigh] = useState(0);
  const [rightColWeigh, setRightColWeigh] = useState(0);
  useEffect(() => {
    resizeMonitor();
    const _debounce = debounce(resizeMonitor, 500);
    window.addEventListener('resize', _debounce);
    return () => {
      window.removeEventListener('resize', _debounce);
    };
  }, []);

  useEffect(() => {
    // 每一侧的格口总高度
    let leftColSumHeight = 0;
    let rightColSumHeight = 0;
    const leftGrids: any[] = [];
    const rightGrids: any[] = [];
    let minLeftHeight = 0;
    let minRightHeight = 0;
    gridList.map((item: any) => {
      const grid = Object.assign({}, item);
      grid.lable = grid.gridNo;
      const palletStr = grid.palletList?.map((p: any) => p.name).join(';');
      grid.lable += palletStr ? ` (${palletStr})` : '';
      if (grid.side === '左侧') {
        leftGrids.push(grid);
        leftColSumHeight += parseInt(grid.height);
        if (parseInt(grid.height) < minLeftHeight || minLeftHeight == 0) {
          minLeftHeight = parseInt(grid.height);
        }
      }
      if (grid.side === '右侧') {
        rightGrids.push(grid);
        rightColSumHeight += parseInt(grid.height);
        if (parseInt(grid.height) < minRightHeight || minRightHeight == 0) {
          minRightHeight = parseInt(grid.height);
        }
      }
    });
    // 单侧一列的格口高度 = 格口总高度/列数   单侧一列的格口宽度 = 货箱宽度/列数
    let tempLeftColHeigh: any = 0;
    let tempLeftColWeigh: any = 0;
    let tempRightColHeigh: any = 0;
    let tempRightColWeigh: any = 0;
    const leftBoxColumnNum = gridPositionValue.leftBoxColumnNum;
    if (leftBoxColumnNum) {
      tempLeftColHeigh = (leftColSumHeight / leftBoxColumnNum).toFixed(2);
      tempLeftColWeigh = (caseSize.width / leftBoxColumnNum).toFixed(2);
      setLeftColWeigh(tempLeftColWeigh);
    }
    const rightBoxColumnNum = gridPositionValue.rightBoxColumnNum;
    if (rightBoxColumnNum) {
      tempRightColHeigh = (rightColSumHeight / rightBoxColumnNum).toFixed(2);
      tempRightColWeigh = (caseSize.width / rightBoxColumnNum).toFixed(2);
      setRightColWeigh(tempRightColWeigh);
    }
    setLeftColArr(
      setLeftCaseData(
        leftGrids,
        tempLeftColHeigh,
        tempLeftColWeigh,
        minLeftHeight,
      ),
    );
    setRightColArr(
      setLeftCaseData(
        rightGrids,
        tempRightColHeigh,
        tempRightColWeigh,
        minRightHeight,
      ),
    );
  }, [refreshPreview, caseSize]);
  const setLeftCaseData = (
    leftGrids: any[],
    tempLeftColHeigh: any,
    tempLeftColWeigh: any,
    minHeight: any,
  ) => {
    let leftTempH = 0;
    let leftColIdx = 0;
    const tempLeftColArr: any[] = [];
    leftGrids.map((item: any, idx: number) => {
      const grid = Object.assign({}, item);
      grid.drawHeight = (
        (grid.height / tempLeftColHeigh) *
        caseSize.height
      ).toFixed(2); // 绘制的像素高度
      const tempArr = tempLeftColArr[leftColIdx]
        ? JSON.parse(JSON.stringify(tempLeftColArr[leftColIdx]))
        : [];
      grid.starLeft = leftColIdx * tempLeftColWeigh;
      grid.top = ((leftTempH / tempLeftColHeigh) * caseSize.height).toFixed(2);
      leftTempH += parseInt(grid.height);
      // 判断是否是最后一个格子:a.根据下标判断 b.累计格子高度+下一个格子高度大于最小列高的1/3,下一个格子是下一列的
      if (leftGrids.length === idx + 1) {
        grid.isLast = true;
      } else {
        const lastGrid = leftGrids[idx + 1];
        const nextSumHeight = leftTempH + parseInt(lastGrid.height);
        if (nextSumHeight - tempLeftColHeigh > minHeight / 3) {
          grid.isLast = true;
        }
      }

      tempArr.push(grid);
      tempLeftColArr[leftColIdx] = tempArr;
      if (grid.isLast && leftGrids.length > idx + 1) {
        leftColIdx++;
        leftTempH = 0;
      }
    });
    return JSON.parse(JSON.stringify(tempLeftColArr));
  };
  const resizeMonitor = () => {
    if (document.body.clientWidth < 1600) {
      imgStyle.width = 438;
      imgStyle.height = 310;
      gridtstyle.left = { ...smallGridListPosition, left: 98 };
      gridtstyle.right = { ...smallGridListPosition, left: 109 };
      caseSize.width = smallGridListPosition.width;
      caseSize.height = smallGridListPosition.height;
    } else {
      imgStyle.width = 584;
      imgStyle.height = 413;
      gridtstyle.left = { ...bigGridListPosition, left: 131 };
      gridtstyle.right = { ...bigGridListPosition, left: 145 };
      caseSize.width = bigGridListPosition.width;
      caseSize.height = bigGridListPosition.height;
    }
    setImgStyle({ ...imgStyle });
    setGridtStyle({ ...gridtstyle });
    setCaseSize({ ...caseSize });
  };
  return (
    <Row style={{ minWidth: 800 }}>
      <Col span={12} className="flex flex_pack_center">
        <div className="car-box">
          <img
            alt=""
            style={{ marginTop: '20px' }}
            width={imgStyle.width}
            height={imgStyle.height}
            src={leftImg}
          />
          <div className="grid-list" style={{ ...gridtstyle.left }}>
            <div className="grid-case">
              {leftColArr.map((col: any, cloIdx: number) => {
                const colLeft = cloIdx * leftColWeigh;
                if (cloIdx > 0) {
                  return (
                    <div
                      className="case-col"
                      style={{ left: `${colLeft}px` }}
                      key={`col_$${cloIdx}`}
                    >
                      {' '}
                    </div>
                  );
                }
              })}
              {leftColArr.map((col: any) => {
                return (
                  col &&
                  col.map((row: any, rowIdx: any) => {
                    return (
                      <div
                        className="case-row flex flex_pack_center flex_orient_center"
                        style={{
                          top: `${row.top}px`,
                          left: `${row.starLeft}px`,
                          width: `${leftColWeigh}px`,
                          height: `${row.drawHeight}px`,
                          borderBottom: `${
                            row.isLast ? '' : '1px solid #999999'
                          }`,
                        }}
                        key={`row_$${rowIdx}`}
                      >
                        <div className="lock-row">{row.lockNo}</div>
                        {row.lable}
                      </div>
                    );
                  })
                );
              })}
            </div>
          </div>
          <Row className="flex flex_pack_center">左侧货箱</Row>
        </div>
      </Col>
      <Col span={12} className="flex flex_pack_center">
        <div className="car-box">
          <img
            alt=""
            style={{ marginTop: '20px' }}
            width={imgStyle.width}
            height={imgStyle.height}
            src={rightImg}
          />
          <div className="grid-list" style={{ ...gridtstyle.right }}>
            <div className="grid-case">
              {rightColArr.map((col: any, cloIdx: number) => {
                const colLeft = cloIdx * rightColWeigh;
                if (cloIdx > 0) {
                  return (
                    <div
                      className="case-col"
                      style={{ left: `${colLeft}px` }}
                      key={`col_$${cloIdx}`}
                    >
                      {' '}
                    </div>
                  );
                }
              })}
              {rightColArr.map((col: any) => {
                return (
                  col &&
                  col.map((row: any, rowIdx: any) => {
                    return (
                      <div
                        className="case-row flex flex_pack_center flex_orient_center"
                        style={{
                          top: `${row.top}px`,
                          left: `${row.starLeft}px`,
                          width: `${rightColWeigh}px`,
                          height: `${row.drawHeight}px`,
                          borderBottom: `${
                            row.isLast ? '' : '1px solid #999999'
                          }`,
                        }}
                        key={`row_$${rowIdx}`}
                      >
                        <div className="lock-row">{row.lockNo}</div>
                        {row.lable}
                      </div>
                    );
                  })
                );
              })}
            </div>
          </div>
          <Row className="flex flex_pack_center">右侧货箱</Row>
        </div>
      </Col>
    </Row>
  );
};
export default React.memo(Preview);
