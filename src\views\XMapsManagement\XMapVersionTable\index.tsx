import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { DEFAULT_PAGINATION, xMapVersionColumns } from '../utils/column';
import XMapManageFetch from '../service';
import { Table, Select } from 'antd';
import { HttpStatusCode } from '@/fetch/core/constant';
import { isNullObject, showMsg } from '@/utils/utils';
import { XMapVersionDataType } from '../utils/type';
import { useMapIdList } from '../utils/utils';
import './index.scss';
const fetchApi = new XMapManageFetch();

const XMapVersionManagement = () => {
  const navigator = useNavigate();
  const mapIdList = useMapIdList();
  const [search, setSearch] = useSearchParams();
  const [versionData, setVersionData] = useState<XMapVersionDataType[]>([]);
  const [searchConditions, setSearchConditions] = useState<{
    currentPage: number;
    pageSize: number;
    mapId?: number | null;
  }>({
    ...DEFAULT_PAGINATION,
  });

  const [total, setTotal] = useState<number>(0);
  const fetchVersionData = async ({
    mapId,
    currentPage,
    pageSize,
  }: {
    currentPage: number;
    pageSize: number;
    mapId?: number | null;
  }) => {
    const res = await fetchApi.fetchXMapVersionData({
      mapId,
      currentPage,
      pageSize,
    });
    if (res.code === HttpStatusCode.Success) {
      if (!isNullObject(res.data)) {
        setVersionData(res.data?.list);
        setTotal(res.data?.total);
      } else {
        setVersionData([]);
        showMsg({
          msg: res.message,
          type: 'success',
        });
      }
    } else {
      setVersionData([]);
      showMsg({
        msg: res.message,
        type: 'error',
      });
    }
  };
  const onChangeSearchCondition = (
    mapId: number | null,
    currentPage = DEFAULT_PAGINATION.currentPage,
    pageSize = DEFAULT_PAGINATION.pageSize,
  ) => {
    const id = Number(search.get('id'));
    setSearchConditions({
      mapId,
      currentPage,
      pageSize,
    });
    if (!mapId && id) {
      navigator('/app/xMapVersionManagement');
    }
    return;
  };

  useEffect(() => {
    const id = Number(search.get('id'));
    if (id) {
      if (!searchConditions.mapId) {
        fetchVersionData({ ...searchConditions, mapId: id });
      } else {
        fetchVersionData({ ...searchConditions });
      }
    } else {
      fetchVersionData({ ...searchConditions });
    }
  }, [searchConditions, search]);
  return (
    <>
      <div className="form-table-container">
        <span className="search-zone">
          地图名称：
          <Select
            showSearch
            placeholder="请选择要查看的地图"
            allowClear
            options={mapIdList}
            onChange={(value) => {
              onChangeSearchCondition(value);
            }}
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
            style={{ flexGrow: 1 }}
          ></Select>
        </span>
        <Table
          bordered
          className="data-zone"
          columns={xMapVersionColumns}
          dataSource={versionData}
          onChange={(pagination) => {
            setSearchConditions({
              ...searchConditions,
              currentPage: pagination.current as number,
              pageSize: pagination.pageSize as number,
            });
          }}
          pagination={{
            current: searchConditions.currentPage,
            pageSize: searchConditions.pageSize,
            total: total,
            pageSizeOptions: [100, 200, 300],
          }}
          scroll={{ y: 700 }}
          rowKey={(item) => {
            return `${item.modifyTime}_${item.id}`;
          }}
        ></Table>
      </div>
    </>
  );
};
export default React.memo(XMapVersionManagement);
