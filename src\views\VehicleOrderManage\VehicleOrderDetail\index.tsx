import React, { useState, useEffect, useRef, useMemo } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Card, Button, message, Spin } from 'antd';
import { UnorderedListOutlined } from '@ant-design/icons';
import { CommonForm } from '@jd/x-coreui';
import { BreadCrumb, showModal } from '@/components';
import UpdateLogModal from './components/UpdateLogModal';
import { vehicleOrderManageApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';
import { transferDateStringToDayJs } from '@/utils/utils';
import {
  supplierInfoFormConfig,
  demandInfoFormConfig,
  orderInfoFormConfig,
  shippingInfoFormConfig,
  OrderStatus,
} from '../utils/constant';
import './index.scss';

const VehicleOrderDetail: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [detailData, setDetailData] = useState<any>(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [formConfigs, setFormConfigs] = useState<any>({
    supplier: supplierInfoFormConfig,
    demand: demandInfoFormConfig,
    order: orderInfoFormConfig,
    shipping: shippingInfoFormConfig,
  });

  const shippingFormRef = useRef<any>(null);

  const orderId = searchParams.get('id');
  const status = searchParams.get('status');

  const breadcrumbItems = [
    {
      title: '订单管理',
      route: '/app/vehicleOrderManage',
    },
    {
      title: '查看订单详情',
      route: '',
    },
  ];

  useEffect(() => {
    if (Number(orderId)) {
      loadOrderDetail();
    }
  }, [orderId]);

  // 加载订单详情
  const loadOrderDetail = async () => {
    setLoading(true);
    try {
      const res = await vehicleOrderManageApi.getDetailById(Number(orderId));
      if (res && res.code === HttpStatusCode.Success && res.data) {
        console.log('拿到的热水。data', res.data);
        setDetailData(res.data);
        initializeFormConfig(res.data);
      } else {
        message.error(res.message || '获取订单详情失败');
      }
    } catch (error) {
      console.error('加载订单详情失败:', error);
      message.error('加载订单详情失败');
    } finally {
      setLoading(false);
    }
  };

  const initializeFormConfig = (data: any, lastestEditModule?: boolean) => {
    const isReadonly =
      lastestEditModule !== undefined ? !lastestEditModule : !isEditMode;
    // 发运信息配置
    const shippingConfig = {
      ...shippingInfoFormConfig,
      fields: shippingInfoFormConfig.fields.map((field: any) => {
        const newField = {
          ...field,
          type: isReadonly ? 'text' : field.type,
        };
        if (isReadonly && field.fieldName === 'estimatedArrivalTime') {
          newField.formatFieldValue = () => {
            return data?.estimatedArrivalTime || '-';
          };
        }
        return newField;
      }),
    };
    if (lastestEditModule) {
      // 只需要修改发运信息的配置
      console.log('此时为啥没有==', {
        ...formConfigs,
        shipping: shippingConfig,
      });
      setFormConfigs({
        ...formConfigs,
        shipping: shippingConfig,
      });
      return;
    }
    // 供应商信息配置
    const supplierConfig = {
      ...supplierInfoFormConfig,
      fields: supplierInfoFormConfig.fields.map((field: any) => ({
        ...field,
        type: isReadonly ? 'text' : field.type,
      })),
    };
    // 用车需求信息配置
    const demandConfig = {
      ...demandInfoFormConfig,
      fields: demandInfoFormConfig.fields.map((field: any) => {
        const newField = {
          ...field,
          type: isReadonly ? 'text' : field.type,
        };
        return newField;
      }),
    };
    // 订单信息配置
    const orderConfig = {
      ...orderInfoFormConfig,
      fields: orderInfoFormConfig.fields.map((field: any) => {
        const newField = {
          ...field,
          type: isReadonly ? 'text' : field.type,
        };
        return newField;
      }),
    };
    setFormConfigs({
      supplier: supplierConfig,
      demand: demandConfig,
      order: orderConfig,
      shipping: shippingConfig,
    });
  };

  const handleShippingEdit = () => {
    setIsEditMode(true);
    initializeFormConfig(detailData, true);
  };

  const handleReset = () => {
    if (shippingFormRef.current && detailData) {
      shippingFormRef.current.setFieldsValue({
        driverName: detailData.driverName,
        contactInfo: detailData.contactInfo,
        estimatedArrivalTime: transferDateStringToDayJs(
          detailData?.estimatedArrivalTime,
        ),
        vehiclePlateNumber: detailData.vehiclePlateNumber,
        vehicleModel: detailData.vehicleModel,
        idNumber: detailData.idNumber,
        insurance: detailData.insurance,
        loadAddress: detailData.loadAddress,
      });
    }
    message.success('已重置为原始数据');
  };

  // 提交编辑
  const handleSubmit = async () => {
    if (!Number(orderId) || !detailData) {
      message.error('订单信息异常');
      return;
    }
    if (!shippingFormRef.current) {
      message.error('表单异常');
      return;
    }
    try {
      const values = await shippingFormRef.current.validateFields();
      setSubmitting(true);
      console.log('此时的detailData.version===', detailData);
      const submitData = {
        ordertId: Number(orderId),
        version: detailData.version,
        currentData: {
          userName: values.driverName,
          contact: values.contactInfo,
          idNumber: values.idNumber || '',
          insurance: values.insurance || 0,
          vehiclePlateNumber: values.vehiclePlateNumber || '',
          vehicleModel: values.vehicleModel || '',
          estimatedArrivalTime: values.estimatedArrivalTime
            ? values.estimatedArrivalTime.format('YYYY-MM')
            : null,
          loadAddress: values.loadAddress || '',
        },
      };
      const response = await vehicleOrderManageApi.editOrder(submitData);
      if (response.code === HttpStatusCode.Success) {
        message.success('编辑成功');
        setIsEditMode(false);
        navigate('/app/vehicleOrderManage');
      } else {
        message.error(response.message || '编辑失败');
      }
    } catch (error: any) {
      if (error.errorFields) {
        message.error('请检查表单填写是否正确');
      } else {
        console.error('编辑失败:', error);
        message.error('编辑失败');
      }
    } finally {
      setSubmitting(false);
    }
  };

  const handleConfigureEdit = () => {
    console.log('此时的detailData.version===', detailData);
    if (!detailData || !detailData.vehicleNameList) {
      message.error('配车信息异常');
      return;
    }
    const params = new URLSearchParams({
      id: orderId?.toString() || '',
      count: detailData.count?.toString(),
      vehicleModel: detailData.vehicleModelTypeName || '',
    });
    navigate(`/app/vehicleOrderManage/configureVehicle?${params.toString()}`, {
      state: { vehicleNameList: detailData.vehicleNameList },
    });
  };

  const handleShowUpdateLog = () => {
    if (!Number(orderId)) {
      message.error('订单信息异常');
      return;
    }
    showModal({
      title: '修改记录',
      width: 800,
      content: <UpdateLogModal orderId={Number(orderId)} />,
      footer: {
        showCancel: false,
        showOk: true,
        okText: '关闭',
        okFunc: (cb: any) => cb(),
      },
    });
  };

  const renderBottomButtons = useMemo(() => {
    if (isEditMode) {
      return [
        <Button key="reset" onClick={handleReset}>
          重置
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={submitting}
          onClick={handleSubmit}
        >
          提交
        </Button>,
      ];
    } else {
      const baseBtnConfig = [
        <Button key="updateLog" onClick={handleShowUpdateLog}>
          修改记录
        </Button>,
      ];
      if (
        status === OrderStatus.CREATED ||
        status === OrderStatus.WAIT_SHIPMENT
      ) {
        baseBtnConfig.push(
          <Button
            key="configureEdit"
            type="primary"
            onClick={handleConfigureEdit}
          >
            配车编辑
          </Button>,
        );
      } else if (status === OrderStatus.SHIPPED) {
        baseBtnConfig.push(
          <Button
            key="shippingEdit"
            type="primary"
            onClick={handleShippingEdit}
          >
            发运编辑
          </Button>,
        );
      }
      return baseBtnConfig;
    }
  }, [isEditMode, status, detailData, submitting]);

  const formatDefaultValue = useMemo(() => {
    return {
      ...(detailData || {}),
      estimatedArrivalTime: transferDateStringToDayJs(
        detailData?.estimatedArrivalTime,
      ),
      vehicleNameList:
        detailData?.vehicleNameList
          ?.map((vehicle: any) => vehicle.vehicleName)
          .join('、') || '-',
    };
  }, [detailData]);

  return (
    <div className="vehicle-order-detail">
      <div style={{ padding: '15px 0' }}>
        <BreadCrumb items={breadcrumbItems} />
      </div>
      <Card className="info-card">
        <div className="card-header">
          <UnorderedListOutlined className="header-icon" />
          <span className="header-title">供应商信息</span>
        </div>
        <div className="card-content">
          <CommonForm
            formConfig={formConfigs.supplier}
            layout="inline"
            formType="edit"
            name="supplier-form"
            defaultValue={formatDefaultValue}
          />
        </div>
      </Card>
      <Card className="info-card" loading={loading}>
        <div className="card-header">
          <UnorderedListOutlined className="header-icon" />
          <span className="header-title">用车需求信息</span>
        </div>
        <div className="card-content">
          <CommonForm
            formConfig={formConfigs.demand}
            layout="inline"
            formType="edit"
            name="demand-form"
            defaultValue={formatDefaultValue}
          />
        </div>
      </Card>
      <Card className="info-card" loading={loading}>
        <div className="card-header">
          <UnorderedListOutlined className="header-icon" />
          <span className="header-title">订单信息</span>
        </div>
        <div className="card-content">
          <CommonForm
            formConfig={formConfigs.order}
            layout="inline"
            formType="edit"
            name="order-form"
            defaultValue={formatDefaultValue}
          />
        </div>
      </Card>
      {(status === OrderStatus.SHIPPED ||
        status === OrderStatus.ARRIVED ||
        status === OrderStatus.COMPLETED) && (
        <Card className="info-card" loading={loading}>
          <div className="card-header">
            <UnorderedListOutlined className="header-icon" />
            <span className="header-title">发运信息</span>
          </div>
          <div className="card-content">
            <CommonForm
              formConfig={formConfigs.shipping}
              layout="inline"
              formType="edit"
              name="shipping-form"
              defaultValue={formatDefaultValue}
              getFormInstance={(ref) => {
                shippingFormRef.current = ref;
              }}
            />
          </div>
        </Card>
      )}
      <div className="bottom-actions">{renderBottomButtons}</div>
    </div>
  );
};

export default React.memo(VehicleOrderDetail);
