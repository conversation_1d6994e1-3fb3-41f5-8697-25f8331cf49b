import { FormConfig } from '@jd/x-coreui';

// 表格列配置
export const tableColumns: any[] = [
  {
    title: '省市区',
    dataIndex: 'countryName',
    align: 'left',
    width: 150,
    fixed: 'left',
  },
  {
    title: '省区片区',
    dataIndex: 'areaName',
    align: 'left',
    width: 150,
    fixed: 'left',
  },
  {
    title: '站点名称',
    dataIndex: 'stationName',
    align: 'left',
    width: 120,
    fixed: 'left',
  },
  {
    title: '站点编号',
    dataIndex: 'stationNumber',
    align: 'left',
    width: 120,
    fixed: 'left',
  },
  {
    title: '线路名称',
    dataIndex: 'taskName',
    align: 'left',
    width: 150,
  },
  {
    title: '类型',
    dataIndex: 'taskRouteType',
    align: 'left',
    width: 100,
  },
  {
    title: '应用场景',
    dataIndex: 'applicateScene',
    align: 'left',
    width: 120,
  },
  {
    title: '所用车型',
    dataIndex: 'vehicleTypeName',
    align: 'left',
    width: 150,
  },
  {
    title: '起点',
    dataIndex: 'startPoint',
    align: 'left',
    width: 150,
  },
  {
    title: '终点',
    dataIndex: 'endPoint',
    align: 'left',
    width: 150,
  },
  {
    title: '线路详情',
    dataIndex: 'roadNames',
    align: 'left',
    width: 200,
  },
  {
    title: '时长(分钟)',
    dataIndex: 'duration',
    align: 'left',
    width: 80,
  },
  {
    title: '里程(千米)',
    dataIndex: 'totalMileage',
    align: 'left',
    width: 80,
  },
  {
    title: '地图供应商',
    dataIndex: 'supplier',
    align: 'left',
    width: 120,
  },
  {
    title: '预期完成时间',
    dataIndex: 'estCompleteTime',
    align: 'left',
    width: 120,
  },
  {
    title: '创建时间',
    dataIndex: 'taskSubmitTime',
    align: 'left',
    width: 150,
  },
  {
    title: '任务状态',
    dataIndex: 'taskStatusName',
    align: 'left',
    width: 100,
    fixed: 'right',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 150,
    align: 'left',
    fixed: 'right',
  },
];

// 地图供应商选项
export const mapVendorOptions = [
  { label: '宽凳', value: 'KUANDEN' },
  { label: '全道', value: 'QUANDAO' },
  { label: '航天宏图', value: 'HTMACRO' },
  { label: '其他', value: 'OTHER' },
];

// 搜索表单配置
export const searchConfig: FormConfig = {
  fields: [
    {
      fieldName: 'provinceCityCountry',
      label: '省市区',
      placeholder: '请选择省市区',
      type: 'cascader',
      mapRelation: { label: 'name', value: 'id', children: 'children' },
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'provinceAgencyArea',
      label: '省区片区',
      placeholder: '请选择省区片区',
      type: 'cascader',
      mapRelation: { label: 'name', value: 'code', children: 'children' },
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'stationNumber',
      label: '站点名称',
      placeholder: '请选择站点',
      type: 'select',
      showSearch: true,
      labelInValue: false,
      mapRelation: { label: 'stationName', value: 'stationNumber' },
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'vehicleModelType',
      label: '所用车型',
      placeholder: '请选择车型',
      type: 'select',
      showSearch: true,
      labelInValue: false,
      mapRelation: { label: 'vehicleModelName', value: 'vehicleModelType' },
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'mapVendor',
      label: '地图供应商',
      placeholder: '请选择地图供应商',
      type: 'select',
      options: mapVendorOptions,
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'durationValue',
      label: '时长',
      placeholder: '请输入时长',
      type: 'inputNumber',
      min: 0,
      precision: 0,
      addonAfter: '分钟',
      controls: true,
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'mileageValue',
      label: '里程',
      placeholder: '请输入里程',
      type: 'inputNumber',
      min: 0,
      precision: 1,
      addonAfter: '千米',
      controls: true,
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'taskSubmitTime',
      label: '提报时间',
      type: 'rangeTime',
      picker: 'date',
      format: 'YYYY-MM-DD',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
  ],
};

export enum TaskStatus {
  TOTAL = 'TOTAL',
  TO_REPORT = 'TO_REPORT',
  TO_COLLECT = 'TO_COLLECT',
  COLLECTING = 'COLLECTING',
  TO_DRAW = 'TO_DRAW',
  DRAWING = 'DRAWING',
  VERIFYING = 'VERIFYING',
  COMPLETED = 'COMPLETED',
}

// Tab配置
export const tabsConfig = [
  {
    key: '',
    label: '全部',
    statusKey: TaskStatus.TOTAL,
  },
  {
    key: 'TO_REPORT',
    label: '待提报',
    statusKey: TaskStatus.TO_REPORT,
  },
  {
    key: 'TO_COLLECT',
    label: '待采图',
    statusKey: TaskStatus.TO_COLLECT,
  },
  {
    key: 'COLLECTING',
    label: '采图中',
    statusKey: TaskStatus.COLLECTING,
  },
  {
    key: 'TO_DRAW',
    label: '待制图',
    statusKey: TaskStatus.TO_DRAW,
  },
  {
    key: 'DRAWING',
    label: '制图中',
    statusKey: TaskStatus.DRAWING,
  },
  {
    key: 'VERIFYING',
    label: '验图中',
    statusKey: TaskStatus.VERIFYING,
  },
  {
    key: 'COMPLETED',
    label: '已完成',
    statusKey: TaskStatus.COMPLETED,
  },
];

export const statusNameStyle = {
  [TaskStatus.TO_REPORT]: {
    textColor: 'rgba(128, 161, 245, 1)',
    bgColor: 'rgba(128, 161, 245, 0.1)',
  },
  [TaskStatus.TO_COLLECT]: {
    textColor: 'rgba(255, 165, 0, 1)',
    bgColor: 'rgba(255, 165, 0, 0.1)',
  },
  [TaskStatus.COLLECTING]: {
    textColor: 'rgba(135, 206, 235, 1)',
    bgColor: 'rgba(135, 206, 235, 0.1)',
  },
  [TaskStatus.TO_DRAW]: {
    textColor: 'rgba(255, 140, 0, 1)',
    bgColor: 'rgba(255, 140, 0, 0.1)',
  },
  [TaskStatus.DRAWING]: {
    textColor: 'rgba(65, 105, 225, 1)',
    bgColor: 'rgba(65, 105, 225, 0.1)',
  },
  [TaskStatus.VERIFYING]: {
    textColor: 'rgba(138, 43, 226, 1)',
    bgColor: 'rgba(138, 43, 226, 0.1)',
  },
  [TaskStatus.COMPLETED]: {
    textColor: 'rgba(26, 181, 98, 1)',
    bgColor: 'rgba(26, 181, 98, 0.1)',
  },
};

// 默认隐藏的列
export const defaultHiddenColumns = [
  'countryName',
  'areaName',
  'stationNumber',
];

// 默认固定在左侧的列
export const defaultLeftFixedColumns = ['stationName'];

// 默认固定在右侧的列
export const defaultRightFixedColumns = ['taskStatusName', 'operation'];

// 领取任务表单配置
export const acceptTaskFormConfig: FormConfig = {
  fields: [
    {
      fieldName: 'contactUser',
      label: '责任人',
      placeholder: '请输入责任人',
      type: 'input',
      validatorRules: [
        {
          required: true,
          message: '请输入责任人',
        },
      ],
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
    },
    {
      fieldName: 'contactPhone',
      label: '联系方式',
      placeholder: '请输入联系方式',
      type: 'input',
      validatorRules: [
        {
          required: true,
          message: '请输入联系方式',
        },
      ],
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
    },
    {
      fieldName: 'mapVendor',
      label: '地图供应商',
      placeholder: '请选择地图供应商',
      type: 'select',
      validatorRules: [
        {
          required: true,
          message: '请选择地图供应商',
        },
      ],
      labelInValue: false,
      showSearch: true,
      options: mapVendorOptions,
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
    },
    {
      fieldName: 'estCompleteTime',
      label: '预期完成时间',
      placeholder: '请选择预期完成时间',
      type: 'datePicker',
      picker: 'date',
      format: 'YYYY-MM-DD',
      validatorRules: [
        {
          required: true,
          message: '请选择预期完成时间',
        },
      ],
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
    },
  ],
};

// 完成任务表单配置
export const completeTaskFormConfig: FormConfig = {
  fields: [
    {
      fieldName: 'contactUser',
      label: '责任人',
      placeholder: '请输入责任人',
      type: 'input',
      validatorRules: [
        {
          required: true,
          message: '请输入责任人',
        },
      ],
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
    },
    {
      fieldName: 'contactPhone',
      label: '联系方式',
      placeholder: '请输入联系方式',
      type: 'input',
      validatorRules: [
        {
          required: true,
          message: '请输入联系方式',
        },
      ],
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
    },
    {
      fieldName: 'completeTime',
      label: '实际完成时间',
      placeholder: '请选择实际完成时间',
      type: 'datePicker',
      picker: 'date',
      format: 'YYYY-MM-DD',
      validatorRules: [
        {
          required: true,
          message: '请选择预期完成时间',
        },
      ],
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
    },
  ],
};
