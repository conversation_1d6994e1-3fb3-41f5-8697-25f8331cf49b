import React, { useCallback, useState } from 'react';
import { CommonTableRequest } from '@/types/interface';
import { DEFAULT_PAGE } from '@/utils/constant';
import { HttpStatusCode, Method } from '@/fetch/core/constant';
import { request } from '@/fetch/core';
import { useTableData } from '@/components/CommonTable/useTableData';
import { UseStatus, ZoneResponse } from '@/types/resource';
import { CommonTable } from '@/components';
import { columns } from './constants';
import { Modal, message } from 'antd';

const AreaTable = (props: { stationBaseId: number; warehouseNo: string }) => {
  const [tableKey, setTableKey] = useState<string>('0');
  const initSearchCondition: CommonTableRequest = {
    ...DEFAULT_PAGE,
  };
  const [searchCondition, setSearchCondition] =
    useState<any>(initSearchCondition);
  const fetchTable = useCallback(
    (searchOptions: CommonTableRequest) => {
      const { pageNum, pageSize } = searchOptions;
      const urlParams: any = { pageNum, pageSize };
      const requestOptions: RequestOptions = {
        path: '/k2/management/station_warehouse/get_map_zone_page_list',
        method: Method.POST,
        body: { ...urlParams, stationBaseId: props.stationBaseId },
      };
      return request(requestOptions);
    },
    [searchCondition, tableKey],
  );
  const { tableData, loading } = useTableData<CommonTableRequest, ZoneResponse>(
    searchCondition,
    fetchTable,
    tableKey,
  );

  const releaseMapZone = (zoneNo: string) => {
    request({
      path: '/k2/management/station_warehouse/release_map_zone',
      method: 'POST',
      body: {
        zoneNo,
      },
    })
      .then((res) => {
        if (res.code === HttpStatusCode.Success) {
          message.success('释放成功');
        } else {
          res.message && message.error(res.message);
        }
      })
      .catch((e) => {});
  };

  const updateMapZoneEnable = (record: any) => {
    request({
      path: '/k2/management/station_warehouse/update_map_zone_enable',
      method: 'POST',
      body: {
        zoneNo: record.zoneNo,
        enable: record.enable,
        zoneType: record.type,
        mapNumber: record.mapNumber,
        mapVersion: record.mapVersion,
        warehouseNo: props.warehouseNo,
      },
    })
      .then((res) => {
        if (res.code === HttpStatusCode.Success) {
          message.success('操作成功');
          setTableKey(Date.now().toString());
        } else {
          res.message && message.error(res.message);
        }
      })
      .catch((e) => {});
  };

  const formatColumns = (columns: any) => {
    return columns.map((item) => {
      switch (item.dataIndex) {
        case 'enable':
          return {
            ...item,
            render: (text: any, record: any, index: number) => {
              return (
                <span style={{ color: text === 1 ? '#1677ff' : 'gray' }}>
                  {text === 1 ? '启用' : '禁用'}
                </span>
              );
            },
          };
        case 'operate':
          return {
            ...item,
            render: (text: any, record: any, index: number) => {
              const enable = record.useStatus === UseStatus.USE;
              return (
                <>
                  <a
                    style={{
                      color: enable ? '#1677ff' : 'gray',
                    }}
                    onClick={() => {
                      if (!enable) {
                        return;
                      }
                      Modal.confirm({
                        content: '确定要释放吗？',
                        onOk: () => {
                          releaseMapZone(record.zoneNo);
                        },
                      });
                    }}
                  >
                    释放
                  </a>
                  <a
                    onClick={() => {
                      Modal.confirm({
                        content: `确定要${record.enable ? '禁用' : '启用'}吗？`,
                        onOk: () => {
                          updateMapZoneEnable(record);
                        },
                      });
                    }}
                    style={{
                      color: record.enable ? '#1677ff' : 'red',
                    }}
                  >
                    {record.enable ? '禁用' : '启用'}
                  </a>
                </>
              );
            },
          };
        default:
          return {
            ...item,
            render: (text: any) => `${text || '-'}`,
          };
      }
    });
  };

  return (
    <div className="area-table-container">
      <CommonTable
        tableListData={{
          list: tableData?.list || [],
          totalPage: tableData?.pages,
          totalNumber: tableData?.total,
        }}
        columns={formatColumns(columns)}
        loading={loading}
        rowKey={'pointNo'}
        searchCondition={searchCondition}
        onPageChange={(paginationData: any) => {
          setSearchCondition({
            ...searchCondition,
            pageNum: paginationData.pageNum,
            pageSize: paginationData.pageSize,
          });
        }}
      />
    </div>
  );
};

export default React.memo(AreaTable);
