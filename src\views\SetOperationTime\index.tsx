import React, { useState, useEffect } from 'react';
import FormTitle from '@/components/FormTitle';
import CustomButton from '@/components/CustomButton';
import { Form, message, TimePicker } from 'antd';
import './index.scss';
import { createInitialTimeToSecond, formateTimeToSecond } from '@/utils/utils';
import { request } from '@/fetch/core';
import { HttpStatusCode } from '@/fetch/core/constant';

const SetOperationTime = () => {
  const [form] = Form.useForm();

  useEffect(() => {
    getOperstionTime();
  }, []);

  const getOperstionTime = () => {
    request({
      method: 'POST',
      path: '/k2/management/require/getOperationTime',
    }).then((res: any) => {
      if (res && res.code === HttpStatusCode.Success) {
        form.setFieldsValue({
          workTime: createInitialTimeToSecond(
            res.data.startTime,
            res.data.endTime,
          ),
        });
      }
    });
  };

  const onSubmit = async () => {
    const formData = await form.validateFields();
    if (!formData.workTime || formData.workTime.length < 2) {
      message.error('时间段未填全，操作失败！');
      return;
    }
    const startTime = formateTimeToSecond(formData.workTime).startTime;
    const endTime = formateTimeToSecond(formData.workTime).endTime;
    const timeDiff = dealTimeToUnix(endTime) - dealTimeToUnix(startTime);
    if (timeDiff < 3600 * 8) {
      message.error('设置运营时长必须≥8小时！');
    } else {
      const requestParams: RequestOptions = {
        method: `POST`,
        path: '/k2/management/require/setOperationTime',
        body: {
          startTime: startTime,
          endTime: endTime,
        },
      };
      const res = await request(requestParams);
      if (res && res.code === HttpStatusCode.Success) {
        message.success(res.message);
      }
    }
  };

  const dealTimeToUnix = (time: any) => {
    if (time !== null) {
      let t = 0;
      const hour = time.split(':')[0];
      const min = time.split(':')[1];
      const sec = time.split(':')[2];
      t = Number(hour * 3600) + Number(min * 60) + Number(sec);
      return t;
    } else {
      return 0;
    }
  };

  return (
    <div style={{ margin: '10px', backgroundColor: 'white', height: '97%' }}>
      <FormTitle title="运营工作时长设置" />
      <Form
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 16 }}
        form={form}
        style={{ marginTop: '20px' }}
      >
        <Form.Item label={'范围'}>{'全国运营人员'}</Form.Item>
        <Form.Item
          label={'工作时间段'}
          name={'workTime'}
          extra={'说明：用于计算【维修单管理】影响运营时长的范围'}
          required
        >
          <TimePicker.RangePicker />
        </Form.Item>
      </Form>
      <div className="submit-button">
        <CustomButton title="确定" onSubmitClick={() => onSubmit()} />
      </div>
    </div>
  );
};

export default React.memo(SetOperationTime);
