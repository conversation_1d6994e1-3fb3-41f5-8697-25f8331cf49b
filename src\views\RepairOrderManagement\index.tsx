import React, { useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import {
  repairOrderStatus,
  type RepairOrderRequest,
  type RepairOrderResponse,
} from '@/types';
import { DEFAULT_PAGE } from '@/utils/constant';
import { useTableData } from '@/components/CommonTable/useTableData';
import { useCommonDropDown } from '@/utils/hooks';
import { RepairOrderFetchApi } from '@/fetch/business';
import {
  CommonForm,
  TableOperateBtn,
  CommonTable,
  showModal,
  FormConfig,
} from '@/components';
import { repairOrderColumns } from './utils/column';
import { RootState } from '@/redux/store';
import {
  removeSearchValues,
  saveSearchValues,
} from '@/redux/reducer/searchForm';
import {
  formatDateToSecond,
  formatLocation,
  formatOptions,
  isEmpty,
  makeUrlQuery,
} from '@/utils/utils';
import { message } from 'antd';
import { HttpStatusCode } from '@/fetch/core/constant';
import RepairOrderModal from './components/RepairOrderModal';
import { ColumnsType } from 'antd/es/table';
import CheckStatusModal from './components/CheckStatusModal';
import isEqual from 'lodash/isEqual';

const fetchApi = new RepairOrderFetchApi();
const RepairOrderManagement = () => {
  const searchRef = useRef<HTMLDivElement>(null);
  const navigator = useNavigate();
  const dispatch = useDispatch();
  const defaultRepairStatus = [
    {
      value: 1,
      label: '待受理',
    },
    {
      value: 2,
      label: '已受理',
    },
    {
      value: 4,
      label: '维修中',
    },
  ];
  const dropdownData = useCommonDropDown(['REQUIRE_STATUS', 'YES_OR_NO']);
  const formConfig: FormConfig = {
    fields: [
      {
        fieldName: 'number',
        label: '维修单号',
        type: 'input',
        placeholder: '请输入维修单号',
      },
      {
        fieldName: 'deviceName',
        label: '车牌号',
        type: 'input',
        placeholder: '请输入车牌号',
      },
      {
        fieldName: 'reportErp',
        label: '联系人',
        type: 'input',
        placeholder: '请输入联系人erp',
      },
      {
        fieldName: 'stationInfo',
        label: '站点名称',
        placeholder: '请选择站点',
        type: 'cascader',
        labelCol: { span: 6 },
        wrapperCol: { span: 18 },
        mapRelation: { label: 'name', value: 'id', children: 'children' },
        specialFetch: 'station',
        changeOnSelect: false,
      },
      {
        fieldName: 'status',
        label: '维修单状态',
        type: 'select',
        labelInValue: false,
        placeholder: '请选择维修单状态',
        multiple: true,
        options: formatOptions(dropdownData.requireStatusList),
      },
      {
        fieldName: 'isInfluenceOperation',
        label: '是否影响运营',
        labelInValue: false,
        type: 'select',
        placeholder: '请选择',
        options: formatOptions(dropdownData.yesOrNoList),
      },
      {
        fieldName: 'reportTime',
        label: '提报时间',
        type: 'rangeTime',
        placeholder: '请选择提报时间',
      },
    ],
  };
  // urlData有两种：一种是查看维修单过来的；二种是点击邮箱里的维修单链接过来的
  const urlData: any = formatLocation(window.location.search);
  const initSearchCondition: RepairOrderRequest = {
    searchForm: {
      number: urlData.order || null,
      deviceName: urlData.name || null,
      reportErp: null,
      reportStartTime: null,
      reportEndTime: null,
      status:
        urlData.order || urlData.name
          ? []
          : defaultRepairStatus.map((item) => {
              return item.value;
            }),
      isInfluenceOperation: null,
      stationBaseId: null,
    },
    ...DEFAULT_PAGE,
  };
  const historySearchValues = useSelector(
    (state: RootState) => state.searchForm,
  );
  const [searchCondition, setSearchCondition] = useState<RepairOrderRequest>(
    () => {
      return historySearchValues.searchValues
        ? historySearchValues.searchValues
        : initSearchCondition;
    },
  );
  const [tableKey, setTableKey] = useState('');
  const [vehicleId, setVehicleId] = useState<number>();
  const [calibrationModal, setCalibrationModal] = useState(false); // 查看标定检测结果弹窗状态
  const [checkRepairModal, setCheckRepairModal] = useState(false); // 查看维修单弹窗状态
  const [repairOrdernfo, setRequireOrdernfo] = useState<{
    number: string | null;
    status: number | null;
  }>({
    number: null,
    status: null,
  });
  const { tableData, loading } = useTableData<
    RepairOrderRequest,
    RepairOrderResponse
  >(searchCondition, fetchApi.getRepairOrderTable, tableKey);

  /**
   * 点击处理、查看、维修完成、确认按钮
   * @param {RepairOrderResponse} record 当前点击的维修单
   */
  const handleClick = (record: RepairOrderResponse) => {
    setRequireOrdernfo({
      number: record.number,
      status: record.status,
    });
    setCheckRepairModal(true);
  };

  /**
   * 点击开始维修按钮
   * @param {RepairOrderResponse} record 当前点击的维修单
   */
  const showConfirm = (record: RepairOrderResponse) => {
    showModal({
      title: '提示',
      content: '确认要开始维修吗?',
      footer: {
        showOk: true,
        showCancel: true,
        okFunc: (cb) => {
          fetchApi.beginRepair(record.number).then((res) => {
            if (res.code === HttpStatusCode.Success) {
              message.success(res.message);
              setTableKey(new Date().getMilliseconds.toString());
              cb();
            } else {
              message.error(res.message);
            }
          });
        },
        cancelFunc: (cb) => {
          cb();
        },
      },
    });
  };
  const onSearchClick = (values) => {
    const data = {
      searchForm: {
        number: values.number ?? null,
        deviceName: values.deviceName ?? null,
        reportErp: values.reportErp ?? null,
        status: values.status ?? null,
        isInfluenceOperation: values.isInfluenceOperation ?? null,
        stationBaseId: (values.stationInfo && values.stationInfo[2]) ?? null,
        reportStartTime:
          formatDateToSecond(values.reportTime).startTime ?? null,
        reportEndTime: formatDateToSecond(values.reportTime).endTime ?? null,
      },
      ...DEFAULT_PAGE,
    };
    if (isEqual(data, searchCondition)) {
      setTableKey(new Date().getMilliseconds().toString());
    } else {
      setSearchCondition(data);
    }
    dispatch(
      saveSearchValues({
        routeName: location.pathname,
        searchValues: data,
      }),
    );
  };

  const onResetClick = () => {
    if (urlData.order || urlData.name) {
      navigator('/app/repairOrderManagement');
    }
    dispatch(
      removeSearchValues({
        routeName: null,
        searchValues: initSearchCondition,
      }),
    );
    delete initSearchCondition.searchForm.reportStartTime;
    delete initSearchCondition.searchForm.reportEndTime;
    delete initSearchCondition.searchForm.stationBaseId;
    const value = {
      ...initSearchCondition,
      searchForm: {
        ...initSearchCondition.searchForm,
        status: defaultRepairStatus.map((item) => {
          return item.value;
        }),
        stationInfo: null,
        reportTime: null,
        number: null,
        deviceName: null,
      },
    };
    setSearchCondition(value);
  };

  const onPageChange = (value: any) => {
    dispatch(
      saveSearchValues({
        routeName: location.pathname,
        searchValues: value,
      }),
    );
    setSearchCondition(value);
  };
  const formatColumns = (columns: ColumnsType<RepairOrderResponse>) => {
    return columns.map((col: any) => {
      switch (col.dataIndex) {
        case 'index':
          col.render = (
            text: any,
            record: RepairOrderResponse,
            index: number,
          ) =>
            `${
              (searchCondition.pageNum - 1) * searchCondition.pageSize +
              index +
              1
            }`;
          break;
        case 'schedule':
          col.render = (item: any, record: RepairOrderResponse) => {
            return (
              <div className="operate-btn">
                <TableOperateBtn
                  title="处理"
                  handleClick={() => handleClick(record)}
                  show={record.status === repairOrderStatus.TO_BE_ACCEPTED}
                />
                <TableOperateBtn
                  title="开始维修"
                  handleClick={() => showConfirm(record)}
                  show={record.status === repairOrderStatus.ACCEPTED}
                />
                <TableOperateBtn
                  title="查看"
                  handleClick={() => handleClick(record)}
                  show={
                    record.status === repairOrderStatus.NOT_ACCEPTED ||
                    record.status === repairOrderStatus.COMPLETED
                  }
                />
                <TableOperateBtn
                  title="维修完成"
                  handleClick={() => handleClick(record)}
                  show={record.status === repairOrderStatus.REPAIRING}
                />
                <TableOperateBtn
                  title="确认"
                  handleClick={() => handleClick(record)}
                  show={record.status === repairOrderStatus.TO_BE_CONFIRMED}
                />
                <TableOperateBtn
                  title="查看标定结果"
                  handleClick={() => {
                    setVehicleId(record.deviceBaseId);
                    setCalibrationModal(true);
                  }}
                  show={!isEmpty(record.checkStatus)}
                />
                <TableOperateBtn
                  title="查看车辆"
                  handleClick={() => {
                    dispatch(removeSearchValues(null));
                    const searchForm = {
                      name: record.deviceName,
                    };
                    const query = makeUrlQuery(searchForm);
                    const url = `/app/deviceLife?${query}`;
                    navigator(url);
                  }}
                  show={true}
                />
              </div>
            );
          };
          break;
        default:
          col.render = (text: any, record: any) => `${text || '-'}`;
          break;
      }
      return col;
    });
  };
  return (
    <>
      <div className="form-table-wrapper">
        <div ref={searchRef}>
          <CommonForm
            formConfig={formConfig}
            layout="inline"
            formType="search"
            colon={false}
            onSearchClick={onSearchClick}
            defaultValue={searchCondition.searchForm}
            onResetClick={onResetClick}
          />
        </div>

        <CommonTable
          searchRef={searchRef}
          tableKey={tableKey}
          searchCondition={searchCondition}
          tableListData={{
            list: tableData?.list || [],
            totalPage: tableData?.pages,
            totalNumber: tableData?.total,
          }}
          columns={formatColumns(repairOrderColumns)}
          loading={loading}
          rowKey="number"
          onPageChange={(value: any) => onPageChange(value)}
        ></CommonTable>
        {/* 查看标定结果 */}
        {calibrationModal && (
          <CheckStatusModal
            visible={calibrationModal}
            onOk={() => {
              setCalibrationModal(false);
            }}
            onCancel={() => {
              setCalibrationModal(false);
            }}
            id={vehicleId}
          />
        )}

        {/* 查看维修单 */}
        {checkRepairModal && (
          <RepairOrderModal
            repairOrderInfo={repairOrdernfo}
            visible={checkRepairModal}
            handleCloseRepairModal={() => setCheckRepairModal(false)}
            refreshTable={(timeStr) => {
              setTableKey(timeStr);
            }}
          />
        )}
      </div>
    </>
  );
};
export default React.memo(RepairOrderManagement);
