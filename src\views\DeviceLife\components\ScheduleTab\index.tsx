import { message } from 'antd';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { handleVehicleManage } from '@/redux/reducer/vehicleManage';
import { DeviceLifeApi } from '@/fetch/business';
import { vehicleScheduleColumns } from '../../utils/column';
import CheckStatusModal from '@/views/RepairOrderManagement/components/CheckStatusModal';
import { PageType, vehicleStatus } from '../../utils/constant';
import { HttpStatusCode } from '@/fetch/core/constant';
import { TabType } from '../../utils/constant';
import { CommonTable, showModal, TableOperateBtn } from '@/components';
import { useTableData } from '@/components/CommonTable/useTableData';
import { DevicePollResponse, DeviceRequest } from '@/types/deviceLife';
import DivideVehicle from '../../DivideVehicle';
import { isEmpty } from '@/utils/utils';
import { AnyFunc } from '@/global';
interface Props {
  searchForm: any;
  activeTabKey: string;
  click: number;
  setSearchForm: AnyFunc;
  searchRef: any;
  tableKey: string;
  setTableKey: any;
}
const fetchApi = new DeviceLifeApi();
const ScheduleTab = (props: Props) => {
  const {
    searchForm,
    activeTabKey,
    click,
    setSearchForm,
    searchRef,
    tableKey,
    setTableKey,
  } = props;
  const dispatch = useDispatch();
  const [pageType, setPageType] = useState<PageType>(
    PageType.scheduleChangeStation,
  );
  const [showDivideModal, setShowDivideModal] = useState(false);
  const [calibrationModal, setCalibrationModal] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]); // 在用车辆、车辆调度池批量选中的数据keys
  const [selectedRows, setSelectedRows] = useState<any>([]); // 去分配存储分配车辆详细信息
  const [vehicleId, setVehicleId] = useState(null);
  const [vehicleName, setVehicleName] = useState<any>(null);
  const { tableData, loading } = useTableData<
    DeviceRequest,
    DevicePollResponse
  >(searchForm, fetchApi.fetchDevicePoolTable, tableKey);
  useEffect(() => {
    if (activeTabKey === TabType.VEHICLE_SCHEDULE) {
      setSelectedRowKeys([]);
      setSelectedRows([]);
      setSearchForm({
        ...searchForm,
        pageNum: 1,
        pageSize: 10,
      });
    }
  }, [activeTabKey, click]);

  useEffect(() => {
    if (tableData) {
      const temp: any = [];
      if (!isEmpty(selectedRows)) {
        for (let item of selectedRows) {
          for (let row of tableData.list) {
            if (row.id === item.id) {
              temp.push(row);
            }
          }
        }
        setSelectedRows(temp);
      }
    }
  }, [tableData]);
  // 批量交付
  const goDeliverVehicle = () => {
    if (selectedRowKeys.length > 0) {
      // 场景2：至少选择了一辆车，但生命周期不全是待交付或不属于同一个站点
      // 判断是否是同一个站点
      const set = new Set();
      selectedRows.forEach((item: any) => {
        set.add(item.stationName);
      });
      // 判断生命周期是否正确
      const filterList = selectedRows.filter((item: any) => {
        return item.hardwareStatus !== vehicleStatus.RESERVE;
      });
      if (set.size > 1 || filterList.length > 0) {
        message.error(
          '仅车辆生命周期为“待交付”车辆且“同一个站点”，才可操作批量交付！',
        );
        return;
      }
      dispatch(handleVehicleManage(selectedRows));
      setShowDivideModal(true);
      setPageType(PageType.scheduleDeliverStation);
    } else {
      // 场景1：未勾选车辆，
      message.error('请至少选择一条“待交付”的数据进行批量交付！');
    }
  };
  // 批量接收
  const goReceiveVehicle = () => {
    if (selectedRowKeys.length > 0) {
      // 场景2：已选最少1车辆，但生命周期不全是“待接收、解绑待接收、修完待接收”
      const filterList = selectedRows.filter((item: any) => {
        if (
          item.hardwareStatus !== vehicleStatus.RECEIVE &&
          item.hardwareStatus !== vehicleStatus.UNBIND &&
          item.hardwareStatus !== vehicleStatus.REPAIRED
        ) {
          return item;
        }
      });
      if (filterList.length > 0) {
        message.error(
          '仅生命周期全为“待接收、解绑待接收、修完待接收”车辆，才可操作批量接收！',
        );
        return;
      }
      // 场景3：已选最少1辆车，且生命周期全部为“待接收、解绑待接收、修完待接收”，点击后，进到【接收车辆】页面
      dispatch(handleVehicleManage(selectedRows));
      setShowDivideModal(true);
      setPageType(PageType.scheduleReceiveStation);
    } else {
      // 场景1：未勾选车辆，
      message.error(
        '请至少选择一条“待接收、解绑待接收、修完待接收”的数据进行批量接收!',
      );
    }
  };
  // 批量转站
  const goChangeStationVehicle = () => {
    if (selectedRowKeys.length > 0) {
      // 场景2：已选最少1车辆，但生命周期不全是“待交付”
      const filterList = selectedRows.filter((item: any) => {
        if (item.hardwareStatus !== vehicleStatus.RESERVE) {
          return item;
        }
      });
      if (filterList.length > 0) {
        message.error('仅生命周期全为“待交付”车辆，才可操作批量转站！');
        return;
      }
      // 场景3：已选最少1辆车，且生命周期全部为“待交付”，点击后，进到【转站车辆】页面
      dispatch(handleVehicleManage(selectedRows));
      setShowDivideModal(true);
      setPageType(PageType.scheduleChangeStation);
    } else {
      // 场景1：未勾选车辆，
      message.error('请至少选择一条“待交付”的数据进行批量转站!');
    }
  };

  const rowSelection = {
    selectedRowKeys: selectedRowKeys,
    onChange: (selectedRowKeys: React.Key[], selectedRows: any[]) => {
      setSelectedRowKeys(selectedRowKeys);
      setSelectedRows(selectedRows);
    },
  };
  // 点击车辆交付
  const goDeliver = (record: any) => {
    const selectedRowKeys = [
      {
        id: record.id,
        deviceName: record.deviceName,
      },
    ];
    dispatch(handleVehicleManage(selectedRowKeys));
    setShowDivideModal(true);
    setPageType(PageType.scheduleDeliverStation);
  };
  // 点击接收
  const goReceive = (record: any) => {
    const selectedRowKeys = [
      {
        id: record.id,
        deviceName: record.deviceName,
      },
    ];
    dispatch(handleVehicleManage(selectedRowKeys));
    setShowDivideModal(true);
    setPageType(PageType.scheduleReceiveStation);
  };
  // 点击驳回
  const goReject = (record: any) => {
    setVehicleName(record.deviceName);
    showModal({
      content: (
        <div style={{ textAlign: 'center' }}>
          <p>{`确定驳回该车辆${record.deviceName}，回到【车辆生产】吗？`}</p>
        </div>
      ),
      footer: {
        showOk: true,
        showCancel: true,
        okFunc: (cb) => {
          rejectOk(record.deviceName);
          cb();
        },
        cancelFunc: (cb) => {
          setVehicleName(null);
          cb();
        },
      },
    });
  };
  // 点击转站
  const goChangeStation = (record: any) => {
    const selectedRowKeys = [
      {
        id: record.id,
        deviceName: record.deviceName,
      },
    ];
    dispatch(handleVehicleManage(selectedRowKeys));
    setShowDivideModal(true);
    setPageType(PageType.scheduleChangeStation);
  };
  // 驳回成功
  const rejectOk = async (deviceName: string) => {
    try {
      const res: any = await fetchApi.deviceRejectToProduct(deviceName);
      if (res.code === HttpStatusCode.Success) {
        message.success(res.message);
        setTableKey(new Date().getMilliseconds().toString());
      } else {
        message.error(res.message);
      }
    } catch (e) {
      console.log(e);
    } finally {
      setVehicleName(null);
    }
  };
  const formatColumns = () => {
    return vehicleScheduleColumns.map((col: any) => {
      switch (col.dataIndex) {
        case 'order':
          col.render = (text: any, record: any, index: number) =>
            `${(searchForm.pageNum - 1) * searchForm.pageSize + index + 1}`;
          break;
        case 'checkStatusName':
          // eslint-disable-next-line react/display-name
          col.render = (text: any, record: any) => `${text || '-'}`;
          break;
        case 'deviceBusinessTypeName':
          // eslint-disable-next-line react/display-name
          col.render = (text: any, record: any) => `${text || '-'}`;
          break;
        case 'stateName':
          // eslint-disable-next-line react/display-name
          col.render = (text: any, record: any) => `${text || '-'}`;
          break;
        case 'cityName':
          // eslint-disable-next-line react/display-name
          col.render = (text: any, record: any) => `${text || '-'}`;
          break;
        case 'stationName':
          // eslint-disable-next-line react/display-name
          col.render = (text: any, record: any) => `${text || '-'}`;
          break;
        case 'stationUseCaseName':
          // eslint-disable-next-line react/display-name
          col.render = (text: any, record: any) => `${text || '-'}`;
          break;
        case 'isRequireName':
          // eslint-disable-next-line react/display-name
          col.render = (text: any, record: any) => `${text || '-'}`;
          break;
        case 'schedule':
          // eslint-disable-next-line react/display-name
          col.render = (item: any, record: any) => {
            return (
              <div className="operate-btn">
                <TableOperateBtn
                  title="接收"
                  handleClick={() => goReceive(record)}
                  show={
                    record.hardwareStatus === vehicleStatus.RECEIVE ||
                    record.hardwareStatus === vehicleStatus.UNBIND ||
                    record.hardwareStatus === vehicleStatus.REPAIRED
                  }
                />
                <TableOperateBtn
                  title="驳回"
                  handleClick={() => goReject(record)}
                  show={record.hardwareStatus === vehicleStatus.RECEIVE}
                />
                <TableOperateBtn
                  title="转站"
                  handleClick={() => goChangeStation(record)}
                  show={record.hardwareStatus === vehicleStatus.RESERVE}
                />
                <TableOperateBtn
                  title="车辆交付"
                  handleClick={() => goDeliver(record)}
                  show={record.hardwareStatus === vehicleStatus.RESERVE}
                />
                <TableOperateBtn
                  title="查看标定结果"
                  handleClick={() => {
                    setVehicleId(record.id);
                    setCalibrationModal(true);
                  }}
                  show={record.checkStatus}
                />
              </div>
            );
          };
          break;
        default:
          return col;
      }
      return col;
    });
  };
  const middleBtns = [
    {
      show: true,
      title: '批量接收',
      key: 'vehicleReceive',
      onClick: () => goReceiveVehicle(),
    },
    {
      show: true,
      title: '批量转站',
      key: 'vehicleChangeStation',
      onClick: () => goChangeStationVehicle(),
    },
    {
      show: true,
      title: '批量交付',
      key: 'vehicleDeliver',
      onClick: () => goDeliverVehicle(),
    },
  ];
  return (
    <div>
      <CommonTable
        middleBtns={middleBtns}
        searchRef={searchRef}
        rowKey={'id'}
        rowSelection={{ ...rowSelection }}
        columns={formatColumns()}
        tableListData={{
          list: tableData?.list ?? [],
          totalNumber: tableData?.total,
          totalPage: tableData?.pages,
        }}
        loading={loading}
        onPageChange={(paginationData: any) => {
          setSearchForm({
            ...searchForm,
            pageNum: paginationData.pageNum,
            pageSize: paginationData.pageSize,
          });
        }}
      />
      {/* 查看标定结果 */}
      {calibrationModal && (
        <CheckStatusModal
          visible={calibrationModal}
          onOk={() => {
            setCalibrationModal(false);
          }}
          onCancel={() => {
            setCalibrationModal(false);
          }}
          id={vehicleId}
        />
      )}
      {/** 接收/转站/交付弹窗 */}
      {showDivideModal && (
        <DivideVehicle
          pageType={pageType}
          show={showDivideModal}
          setShow={setShowDivideModal}
          setTableKey={setTableKey}
        />
      )}
    </div>
  );
};

export default React.memo(ScheduleTab);
