import React, { useState } from 'react';
import { Outlet } from 'react-router-dom';
import './index.scss';
import Header from './components/Header';
import CommonMenu from './components/CommonMenu';


const MainLayout = () => {
  const [menuVisible, setMenuVisible] = useState(true);

  const changeMenu = () => {
    setMenuVisible(!menuVisible);
  };
  return (
    <div className="main-layout-content">
      <Header />
      <div className="main-body-content">
        <div
          className="main-menu-content"
          style={{
            display: menuVisible ? 'block' : 'none',
          }}
        >
          <CommonMenu />
        </div>

        <div className="menu-wrap">
          <img
            src={
              menuVisible
                ? require('@/assets/image/common/menu-retract.png')
                : require('@/assets/image/common/menu-spread.png')
            }
            className="menu-btn"
            onClick={changeMenu}
          />
        </div>

        <div className="main">
          <div id="qiankun-root"></div>
          <Outlet></Outlet>
        </div>
      </div>
    </div>
  );
};
export default MainLayout;
