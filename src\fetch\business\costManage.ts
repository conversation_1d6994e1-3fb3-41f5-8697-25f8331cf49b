import { request } from '../core';

// 类型定义
export interface CostAllocationSearchForm {
  pageNum: number;
  pageSize: number;
  provinceId?: number | null;
  cityId?: number | null;
  stationId?: number | null;
  startTime?: Date | null;
  endTime?: Date | null;
}

export interface CostAllocationItem {
  costAllocationId: number;
  recordDate: string;
  provinceName: string;
  cityName: string;
  stationName: string;
  stationStatusName: string;
  deploymentCompletionDate: string;
  deviceCount: number;
  expectedRevenue: number | null;
  actualRevenue: number | null | undefined;
  modifyUser: string | null;
  modifyTime: string | null;
  remark: string | null | undefined;
}

export interface CostAllocationEditItem {
  costAllocationId: number;
  actualRevenue?: number | null;
  remark?: string | null;
}

export interface CostAllocationEditParams {
  costAllocationList: CostAllocationEditItem[];
}

class CostManage {
  // 分页获取成本划拨账单
  getCostAllocationPageList(params: CostAllocationSearchForm) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/cost_allocation_info/get_page_list',
      body: params,
    };
    return request(options);
  }

  // 编辑成本划拨账单
  editCostAllocation(params: CostAllocationEditParams) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/cost_allocation_info/edit',
      body: params,
    };
    return request(options);
  }
}

export default new CostManage();
