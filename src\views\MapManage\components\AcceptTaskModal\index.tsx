import React, { forwardRef, useImperative<PERSON>and<PERSON>, useMemo, useRef } from 'react';
import { CommonForm } from '@jd/x-coreui';
import { acceptTaskFormConfig, TaskStatus } from '../../utils/constant';
import { mapManageApi } from '@/fetch/business';
import { isNumber } from '@/utils/utils';
import { HttpStatusCode } from '@/fetch/core/constant';
import { message } from 'antd';
import './index.scss';

interface AcceptTaskModalProps {
  taskId: number;
}

const AcceptTaskModal = forwardRef<any, AcceptTaskModalProps>(
  ({ taskId }, ref) => {
    const formRef = useRef<any>({});

    useImperativeHandle(ref, () => ({
      handleSubmit: async () => {
        if (!isNumber(Number(taskId))) {
          message.error('任务ID异常');
          return;
        }
        try {
          const values = await formRef.current?.validateFields();
          const formattedData = {
            ...values,
            estCompleteTime: values.estCompleteTime?.format('YYYY-MM-DD') || '',
          };
          handleAcceptTaskConfirm(formattedData);
        } catch (error) {
          console.error('表单验证失败:', error);
          message.error('请检查表单填写是否正确');
          throw error;
        }
      },
    }));

    const handleAcceptTaskConfirm = async (formData: any) => {
      try {
        const response = await mapManageApi.acceptTask({
          taskId: Number(taskId),
          ...formData,
        });
        if (response.code === HttpStatusCode.Success) {
          message.success('领取任务成功');
        } else {
          message.error(response.message || '领取任务失败');
        }
      } catch (error) {
        console.error('领取任务失败:', error);
        message.error('领取任务失败');
      }
    };

    return (
      <div className="accept-task-modal">
        <div className="modal-content">
          <CommonForm
            formConfig={acceptTaskFormConfig}
            layout="horizontal"
            formType="edit"
            colon={true}
            labelAlign="right"
            getFormInstance={(ref) => {
              formRef.current = ref;
            }}
          />
        </div>
      </div>
    );
  },
);

export default AcceptTaskModal;
