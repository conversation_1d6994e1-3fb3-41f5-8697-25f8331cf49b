import { FormConfig } from '@jd/x-coreui';

// 表格列配置
export const tableColumns: any[] = [
  {
    title: '省市区',
    dataIndex: 'countryName',
    align: 'left',
    width: 150,
    fixed: 'left',
  },
  {
    title: '省区片区',
    dataIndex: 'areaName',
    align: 'left',
    width: 150,
    fixed: 'left',
  },
  {
    title: '站点名称',
    dataIndex: 'stationName',
    align: 'left',
    width: 120,
    fixed: 'left',
  },
  {
    title: '站点编号',
    dataIndex: 'stationNumber',
    align: 'left',
    width: 120,
    fixed: 'left',
  },
  {
    title: '订单编号',
    dataIndex: 'orderNumber',
    align: 'left',
    width: 150,
  },
  {
    title: '车辆型号',
    dataIndex: 'vehicleModelName',
    align: 'left',
    width: 150,
  },
  {
    title: '下单数量',
    dataIndex: 'count',
    align: 'left',
    width: 100,
  },
  {
    title: '配车数量',
    dataIndex: 'allocationCount',
    align: 'left',
    width: 100,
  },
  {
    title: '发运数量',
    dataIndex: 'dispatchCount',
    align: 'left',
    width: 100,
  },
  {
    title: '配车列表',
    dataIndex: 'vehicleNameList',
    align: 'left',
    width: 150,
  },
  {
    title: '保险列表',
    dataIndex: 'insuranceNoList',
    align: 'left',
    width: 150,
  },
  {
    title: '联系人',
    dataIndex: 'contact',
    align: 'left',
    width: 100,
  },
  {
    title: '联系人手机号',
    dataIndex: 'contactPhone',
    align: 'left',
    width: 130,
  },
  {
    title: '联系地址',
    dataIndex: 'address',
    align: 'left',
    width: 200,
  },
  {
    title: '预期交付月份',
    dataIndex: 'expectedDeliveryMonth',
    align: 'left',
    width: 120,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    align: 'left',
    width: 150,
  },
  {
    title: '更新时间',
    dataIndex: 'modifyTime',
    align: 'left',
    width: 150,
  },
  {
    title: '订单状态',
    dataIndex: 'statusName',
    align: 'left',
    width: 100,
    fixed: 'right',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 150,
    align: 'left',
    fixed: 'right',
  },
];

// 搜索表单配置
export const searchConfig: FormConfig = {
  fields: [
    {
      fieldName: 'provinceCityCountry',
      label: '省市区',
      placeholder: '请选择省市区',
      type: 'cascader',
      mapRelation: { label: 'name', value: 'id', children: 'children' },
    },
    {
      fieldName: 'provinceAgencyArea',
      label: '省区片区',
      placeholder: '请选择省区片区',
      type: 'cascader',
      mapRelation: { label: 'name', value: 'code', children: 'children' },
    },
    {
      fieldName: 'stationNumber',
      label: '站点名称',
      placeholder: '请选择站点',
      type: 'select',
      showSearch: true,
      labelInValue: false,
      mapRelation: { label: 'stationName', value: 'stationNumber' },
    },
    {
      fieldName: 'vehicleModelType',
      label: '车辆型号',
      placeholder: '请选择车辆型号',
      type: 'select',
      showSearch: true,
      labelInValue: false,
      mapRelation: { label: 'vehicleModelName', value: 'vehicleModelType' },
    },
    {
      fieldName: 'orderNumber',
      label: '订单编号',
      placeholder: '请输入订单编号',
      type: 'input',
    },
    {
      fieldName: 'createTime',
      label: '创建日期',
      type: 'rangeTime',
      picker: 'date',
      format: 'YYYY-MM-DD',
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      xxl: 8,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'expectedDeliveryMonth',
      label: '预期交付月份',
      type: 'datePicker',
      picker: 'month',
      format: 'YYYY-MM',
      placeholder: '请选择预期交付月份',
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
      xxl: 8,
      xl: 6,
      lg: 12,
    },
  ],
};

export enum OrderStatus {
  TOTAL = 'TOTAL',
  CREATED = 'CREATED',
  WAIT_SHIPMENT = 'WAIT_SHIPMENT',
  SHIPPED = 'SHIPPED',
  ARRIVED = 'ARRIVED',
  COMPLETED = 'COMPLETED',
}

// Tab配置
export const tabsConfig = [
  {
    key: '',
    label: '全部',
    statusKey: OrderStatus.TOTAL,
  },
  {
    key: 'CREATED',
    label: '已下单',
    statusKey: OrderStatus.CREATED,
  },
  {
    key: 'WAIT_SHIPMENT',
    label: '待发运',
    statusKey: OrderStatus.WAIT_SHIPMENT,
  },
  {
    key: 'SHIPPED',
    label: '已发运',
    statusKey: OrderStatus.SHIPPED,
  },
  {
    key: 'ARRIVED',
    label: '已到车',
    statusKey: OrderStatus.ARRIVED,
  },
  {
    key: 'COMPLETED',
    label: '已完成',
    statusKey: OrderStatus.COMPLETED,
  },
];

export const statusNameStyle = {
  [OrderStatus.CREATED]: {
    textColor: 'rgba(128, 161, 245, 1)',
    bgColor: 'rgba(128, 161, 245, 0.1)',
  },
  [OrderStatus.WAIT_SHIPMENT]: {
    textColor: 'rgba(255, 165, 0, 1)',
    bgColor: 'rgba(255, 165, 0, 0.1)',
  },
  [OrderStatus.SHIPPED]: {
    textColor: 'rgba(135, 206, 235, 1)',
    bgColor: 'rgba(135, 206, 235, 0.1)',
  },
  [OrderStatus.ARRIVED]: {
    textColor: 'rgba(65, 105, 225, 1)',
    bgColor: 'rgba(65, 105, 225, 0.1)',
  },
  [OrderStatus.COMPLETED]: {
    textColor: 'rgba(26, 181, 98, 1)',
    bgColor: 'rgba(26, 181, 98, 0.1)',
  },
};

// 默认隐藏的列
export const defaultHiddenColumns = [
  'countryName',
  'areaName',
  'stationNumber',
];

// 默认固定在左侧的列
export const defaultLeftFixedColumns = ['stationName'];

// 默认固定在右侧的列
export const defaultRightFixedColumns = ['statusName', 'operation'];

// 配车表单配置
export const configureVehicleFormConfig = {
  fields: [
    {
      fieldName: 'vehicleName',
      label: '车辆ID',
      type: 'input',
      placeholder: '请输入车辆ID',
      validatorRules: [{ required: true, message: '请输入车辆id' }],
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'serialNo',
      label: '车架号',
      type: 'input',
      placeholder: '请输入车辆ID自动填充车架号',
      disabled: true,
      validatorRules: [{ required: true, message: '请输入车架号' }],
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'vehicleTypeId',
      label: '车型名称',
      type: 'select',
      placeholder: '请输入车辆ID自动填充车型名称',
      disabled: true,
      validatorRules: [{ required: true, message: '请选择车型名称' }],
      labelInValue: false,
      mapRelation: { label: 'name', value: 'code' },
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'vehicleNumber',
      label: '车牌号',
      type: 'input',
      placeholder: '请输入车牌号',
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
  ],
};

// 填写发运信息表单配置
export const shippingFormConfig: FormConfig = {
  fields: [
    {
      fieldName: 'driverName',
      label: '承运联系人',
      type: 'input',
      placeholder: '请输入承运联系人',
      validatorRules: [{ required: true, message: '请输入承运联系人' }],
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'contact',
      label: '联系电话',
      type: 'input',
      placeholder: '请输入联系电话',
      validatorRules: [
        { required: true, message: '请输入联系电话' },
        {
          pattern: /^[\d-]+$/,
          message: '请输入正确的电话号码格式',
        },
      ],
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'estimatedArrivalTime',
      label: '预计到车时间',
      type: 'datePicker',
      placeholder: '请选择预计到车时间',
      format: 'YYYY-MM-DD',
      picker: 'date',
      validatorRules: [{ required: true, message: '请选择预计到车时间' }],
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'vehiclePlateNumber',
      label: '车牌号',
      type: 'input',
      placeholder: '请输入车牌号',
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'vehicleModel',
      label: '车型名称',
      type: 'input',
      placeholder: '请输入车型名称',
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'idNumber',
      label: '身份证号',
      type: 'input',
      placeholder: '请输入身份证号',
      validatorRules: [
        {
          pattern:
            /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
          message: '请输入正确的身份证号格式',
        },
      ],
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'insuranceMoney',
      label: '承运险',
      type: 'inputNumber',
      placeholder: '请输入承运险金额',
      min: 0,
      precision: 0,
      step: 1,
      addonAfter: '元',
      controls: true,
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'loadAddress',
      label: '装货地址',
      type: 'input',
      placeholder: '请输入装货地址',
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
  ],
};

// 核实收件信息表单配置
export const detailFormConfig: FormConfig = {
  fields: [
    {
      fieldName: 'contact',
      label: '收货人',
      type: 'text',
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'contactPhone',
      label: '联系电话',
      type: 'text',
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'stationName',
      label: '所属站点',
      type: 'text',
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'vehicleNameList',
      label: '关联车辆',
      type: 'text',
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'address',
      label: '配送地址',
      type: 'text',
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'alternateContact',
      label: '备选联系人',
      type: 'text',
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'alternateContactPhone',
      label: '备选手机号',
      type: 'text',
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
  ],
};

// 验收表单配置
export const vehicleAcceptanceFormConfig: FormConfig = {
  fields: [
    {
      fieldName: 'checkUser',
      label: '验收人',
      type: 'input',
      placeholder: '请输入验收人',
      validatorRules: [{ required: true, message: '请输入验收人' }],
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      xxl: 12,
      xl: 12,
      lg: 24,
    },
    {
      fieldName: 'vehicleAppearanceStatus',
      label: '车辆外观是否完好',
      type: 'radioGroup',
      options: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      validatorRules: [{ required: true, message: '请选择车辆外观是否完好' }],
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      xxl: 12,
      xl: 12,
      lg: 24,
    },
    {
      fieldName: 'vehiclePowerStatus',
      label: '车辆电源是否正常',
      type: 'radioGroup',
      options: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      validatorRules: [{ required: true, message: '请选择车辆电源是否正常' }],
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      xxl: 12,
      xl: 12,
      lg: 24,
    },
    {
      fieldName: 'chargerEquippedStatus',
      label: '车辆充电器是否齐全',
      type: 'radioGroup',
      options: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      validatorRules: [{ required: true, message: '请选择车辆充电器是否齐全' }],
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      xxl: 12,
      xl: 12,
      lg: 24,
    },
    {
      fieldName: 'spareKeyEquippedStatus',
      label: '备用钥匙是否齐全',
      type: 'radioGroup',
      options: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      validatorRules: [{ required: true, message: '请选择备用钥匙是否齐全' }],
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      xxl: 12,
      xl: 12,
      lg: 24,
    },
    {
      fieldName: 'imageList',
      label: '验收照片',
      type: 'upload',
      fileListType: 'picture',
      accept: 'image/*',
      LOPDN: process.env.JDX_APP_REQUEST_HEADER!,
      bucketName: 'rover-operation',
      max: 10,
      getPreSignatureUrl:
        location.protocol +
        '//' +
        process.env.JDX_APP_CLOUD_FETCH_DOMAIN +
        '/infrastructure/oss/getPreUrl',
      validatorRules: [{ required: true, message: '请上传验收照片' }],
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      xxl: 24,
      xl: 24,
      lg: 24,
    },
    {
      fieldName: 'checkRemark',
      label: '其他情况说明',
      type: 'textarea',
      placeholder: '请输入其他情况说明',
      autoSize: { minRows: 3, maxRows: 6 },
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      xxl: 24,
      xl: 24,
      lg: 24,
    },
  ],
};

// 供应商信息表单配置
export const supplierInfoFormConfig: FormConfig = {
  fields: [
    {
      fieldName: 'supplier',
      label: '供应商',
      type: 'text',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'supplierUser',
      label: '联系人',
      type: 'text',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'supplierContact',
      label: '联系电话',
      type: 'text',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
  ],
};

// 用车需求信息表单配置
export const demandInfoFormConfig: FormConfig = {
  fields: [
    {
      fieldName: 'contact',
      label: '联系人',
      type: 'text',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'contactPhone',
      label: '联系电话',
      type: 'text',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'mail',
      label: '电子邮箱',
      type: 'text',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'stationName',
      label: '所属站点',
      type: 'text',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'stationNumber',
      label: '站点编号',
      type: 'text',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'address',
      label: '配送地址',
      type: 'text',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'alternateContact',
      label: '备选联系人',
      type: 'text',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'alternateContactPhone',
      label: '备选联系人电话',
      type: 'text',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
  ],
};

// 订单信息表单配置
export const orderInfoFormConfig: FormConfig = {
  fields: [
    {
      fieldName: 'vehicleNameList',
      label: '配车列表',
      type: 'text',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'insuranceNo',
      label: '关联保单',
      type: 'text',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'expectedDeliveryMonth',
      label: '预计交付月份',
      type: 'text',
      format: 'YYYY-MM', // 必填，用于格式转换
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'vehicleModelTypeName',
      label: '车辆型号',
      type: 'text',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'count',
      label: '订单数量',
      type: 'text',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'orderNumber',
      label: '订单编号',
      type: 'text',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
  ],
};

// 发运信息表单配置
export const shippingInfoFormConfig: FormConfig = {
  fields: [
    {
      fieldName: 'driverName',
      label: '承运联系人',
      type: 'input',
      placeholder: '请输入承运联系人',
      validatorRules: [{ required: true, message: '请输入承运联系人' }],
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'contactInfo',
      label: '联系电话',
      type: 'input',
      placeholder: '请输入联系电话',
      validatorRules: [
        { required: true, message: '请输入联系电话' },
        {
          pattern: /^[\d-]+$/,
          message: '请输入正确的电话号码格式',
        },
      ],
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'estimatedArrivalTime',
      label: '预计到车时间',
      type: 'datePicker',
      placeholder: '请选择预计到车时间',
      format: 'YYYY-MM-DD',
      picker: 'date',
      validatorRules: [{ required: true, message: '请选择预计到车时间' }],
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'vehiclePlateNumber',
      label: '车牌号',
      type: 'input',
      placeholder: '请输入车牌号',
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'vehicleModel',
      label: '车型名称',
      type: 'input',
      placeholder: '请输入车型名称',
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'idNumber',
      label: '身份证号',
      type: 'input',
      placeholder: '请输入身份证号',
      validatorRules: [
        {
          pattern:
            /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
          message: '请输入正确的身份证号格式',
        },
      ],
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'insurance',
      label: '承运险',
      type: 'inputNumber',
      placeholder: '请输入承运险金额',
      min: 0,
      precision: 0,
      step: 1,
      addonAfter: '元',
      controls: true,
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'loadAddress',
      label: '装货地址',
      type: 'input',
      placeholder: '请输入装货地址',
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
  ],
};

// 修改记录弹窗表格列配置
export const modifyLogTableColumns = [
  {
    title: '修改人',
    dataIndex: 'userName',
    key: 'userName',
    width: 120,
  },
  {
    title: '修改时间',
    dataIndex: 'modifyTime',
    key: 'modifyTime',
    width: 180,
  },
  {
    title: '修改字段',
    dataIndex: 'fieldName',
    key: 'fieldName',
    width: 120,
  },
  {
    title: '修改前',
    dataIndex: 'beforeValue',
    key: 'beforeValue',
    width: 150,
  },
  {
    title: '修改后',
    dataIndex: 'afterValue',
    key: 'afterValue',
    width: 150,
  },
];
