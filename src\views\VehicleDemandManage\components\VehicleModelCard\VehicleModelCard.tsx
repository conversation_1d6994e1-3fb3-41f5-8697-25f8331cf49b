import React from 'react';
import { ProfileOutlined, CheckCircleFilled } from '@ant-design/icons';
import './VehicleModelCard.scss';

export interface VehicleModelCardProps {
  name: string;
  url: string;
  count: number;
  isActive: boolean;
  onClick: () => void;
}

const VehicleModelCard: React.FC<VehicleModelCardProps> = ({
  name,
  url,
  count,
  isActive,
  onClick,
}) => {
  return (
    <div
      className={`vehicle-model-card ${isActive ? 'active' : ''}`}
      onClick={onClick}
      style={{
        backgroundImage: `url(${url})`,
        opacity: isActive ? 1 : 0.6,
      }}
    >
      <div className="card-content">
        <div className="card-info">
          <div className="left-info">
            <ProfileOutlined className="icon" />
            <span className="name">{name}</span>
          </div>
          <div className="right-info">
            <span className="count">{`+${count}`}</span>
            <CheckCircleFilled className="check-icon" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default React.memo(VehicleModelCard);
