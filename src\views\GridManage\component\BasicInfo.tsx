/* eslint-disable no-unused-vars */
import { Form, FormInstance, Input, Radio, Select } from 'antd';
import React, { useState, useEffect } from 'react';
import { EditModuleTitle } from '@/components';
import { useCommonDropDown } from '@/utils/hooks';
import { dropDownListKey, dropDownKey } from '@/utils/constant';
import { PageType } from '@/utils/EditTitle';
import { GridManageApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';
import { setGridSizeListAction } from '@/redux/reducer/boxGrid';
import { useDispatch } from 'react-redux';
import { formatOptions } from '@/utils/utils';
interface Props {
  form: FormInstance;
  pageType: string;
  initValues: any;
  updateBaseInfoValue?: Function;
  changeProductType: Function;
}

const BasicInfo = ({
  initValues,
  form,
  pageType,
  changeProductType,
  updateBaseInfoValue,
}: Props) => {
  const fetchApi = new GridManageApi();
  const dispatch = useDispatch();
  const [boxNameList, setBoxNameList] = useState([]);
  const [productionType, setProductionType] = useState(null);
  const dropDownData: any = useCommonDropDown([
    dropDownKey.BOX_DRIVER_TYPE,
    dropDownKey.SERIAL_PORT_NUMBER,
    dropDownKey.PRODUCT_TYPE,
    dropDownKey.ENABLE,
  ]);

  useEffect(() => {
    fetchBoxName();
    if (initValues) {
      form.setFieldsValue({
        ...initValues,
        driverType: initValues.driverType
          ? {
              label: initValues.driverType,
              value: initValues.driverType,
            }
          : null,
        hardwareModelId:
          initValues?.hardwareModelId && initValues?.hardwareModelName
            ? {
                label: initValues.hardwareModelName,
                value: initValues.hardwareModelId,
              }
            : null,
      });
      setProductionType(initValues.productType);
    }
    if (initValues && boxNameList) {
      automaticValue(boxNameList, 'boxType', initValues.box);
    }
  }, [initValues]);

  const fetchBoxName = async () => {
    const res = await fetchApi.getBoxNameList();
    if (res.code === HttpStatusCode.Success) {
      setBoxNameList(
        res.data.map((item) => {
          return {
            value: item.hardwareModelId,
            label: item.hardwareModelName,
            key: item.hardwareModelModel,
          };
        }),
      );
    }
  };

  const automaticValue = (arr: any[], key: string, value: any) => {
    const filterList = arr?.filter((ele: any) => ele.code === value);
    if (filterList.length > 0) {
      const setKey: any = {};
      setKey[key] = filterList[0].parent;
      form.setFieldsValue(setKey);
    }
  };

  return (
    <div className="vendor-form-content">
      <EditModuleTitle title={'基础信息'} />
      <Form
        labelCol={{ span: 5 }}
        wrapperCol={{ span: 18 }}
        className="reform"
        form={form}
        autoComplete="off"
      >
        <Form.Item
          name="name"
          label="模板名称"
          rules={[{ required: true, message: '请输入模板名称' }]}
        >
          <Input
            disabled={pageType === PageType.READONLY}
            maxLength={50}
            placeholder={'请输入模板名称，车辆厂商-货箱厂商-40/40-驱动'}
          />
        </Form.Item>
        <Form.Item name="id" label="模板id">
          <Input disabled placeholder="系统生成" />
        </Form.Item>
        <Form.Item
          name="hardwareModelId"
          label="箱体名称"
          rules={[{ required: true, message: '请输入箱体名称' }]}
        >
          <Select
            disabled={pageType === PageType.READONLY}
            placeholder="请输入箱体名称"
            labelInValue
            options={boxNameList}
            onChange={(e) => {
              if (e.value) {
                form.setFieldsValue({
                  hardwareModelModel: e.key,
                });
              }
            }}
          />
        </Form.Item>
        <Form.Item name="hardwareModelModel" label="箱体型号">
          <Input disabled maxLength={50} placeholder="系统生成" />
        </Form.Item>
        <Form.Item
          name="driverType"
          label="驱动类型"
          rules={[{ required: true, message: '请选择驱动类型' }]}
        >
          <Select
            disabled={pageType === PageType.READONLY}
            placeholder="请选择驱动类型"
            labelInValue
            options={dropDownData[dropDownListKey.BOX_DRIVER_TYPE]?.map(
              (item) => {
                return {
                  label: item.name,
                  value: item.code,
                  key: item.parent,
                };
              },
            )}
            onChange={(val) => {
              form.setFieldsValue({
                baudRate: val.key,
              });
            }}
          />
        </Form.Item>
        <Form.Item name="baudRate" label="波特率">
          <Input disabled maxLength={50} placeholder="系统生成" />
        </Form.Item>
        <Form.Item
          name="deviceId"
          label="串口号"
          rules={[{ required: true, message: '请选择串口号' }]}
        >
          <Select
            disabled={pageType === PageType.READONLY}
            placeholder="请选择串口号"
            options={formatOptions(
              dropDownData[dropDownListKey.SERIAL_PORT_NUMBER],
            )}
          />
        </Form.Item>
        <Form.Item
          name={'productType'}
          label={'所属产品'}
          rules={[{ required: true, message: '请选择所属产品' }]}
        >
          <Radio.Group
            disabled={pageType !== PageType.ADD}
            options={formatOptions(dropDownData[dropDownListKey.PRODUCT_TYPE])}
            onChange={(e) => {
              dispatch(
                setGridSizeListAction([{ width: '', length: '', height: '' }]),
              );
              setProductionType(e.target.value);
              changeProductType(e.target.value);
            }}
          />
        </Form.Item>
        {productionType === 'vehicle' && (
          <Form.Item className="boxColumnInstr" label=" " colon={false}>
            <div className="instruction">
              注意：判断货箱左右规则，车前进方向，人在车后
            </div>
          </Form.Item>
        )}

        {productionType === 'vehicle' && (
          <Form.Item
            name="leftBoxColumnNum"
            label="左侧货箱列数"
            rules={[{ required: true, message: '请输入左侧货箱列数' }]}
          >
            <Input
              disabled={pageType === PageType.READONLY}
              placeholder="请输入列数"
              maxLength={1}
              onChange={(e) => {
                let value = e.target.value;
                value = value.replace(/[^0-5]*$/g, '');
                form.setFieldsValue({
                  leftBoxColumnNum: value,
                });
              }}
              onBlur={(e) => {
                updateBaseInfoValue &&
                  updateBaseInfoValue(e.target.value, 'leftBoxColumnNum');
              }}
            />
          </Form.Item>
        )}
        {productionType === 'vehicle' && (
          <Form.Item
            name="rightBoxColumnNum"
            label="右侧货箱列数"
            rules={[{ required: true, message: '请输入右侧货箱列数' }]}
          >
            <Input
              disabled={pageType === PageType.READONLY}
              placeholder="请输入列数"
              maxLength={1}
              onChange={(e) => {
                let value = e.target.value;
                value = value.replace(/[^0-5]*$/g, '');
                form.setFieldsValue({
                  rightBoxColumnNum: value,
                });
              }}
              onBlur={(e) => {
                updateBaseInfoValue &&
                  updateBaseInfoValue(e.target.value, 'rightBoxColumnNum');
              }}
            />
          </Form.Item>
        )}

        <Form.Item
          name={'enable'}
          label={'模板状态'}
          rules={[{ required: true, message: '请选择模板状态' }]}
        >
          <Radio.Group
            disabled={pageType === PageType.READONLY}
            options={formatOptions(dropDownData[dropDownListKey.ENABLE])}
          />
        </Form.Item>
        <Form.Item label=" " colon={false}>
          <div className="instruction">
            状态说明：
            <br />
            1、只有“有效”的箱体格口模板，才能被引用成为一个格口模板选择项，“无效”的箱体格口模板，不能被引用；
            <br />
            2、箱体格口模板状态从“有效”改为“无效”，历史有被引用，历史数据不受影响，再修改，该无效箱体格口模板不在选择项范围内。
          </div>
        </Form.Item>
      </Form>
    </div>
  );
};

export default React.memo(BasicInfo);
