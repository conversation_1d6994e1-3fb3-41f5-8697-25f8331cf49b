import React, { useState, useEffect } from 'react';
import { CommonTable } from '@jd/x-coreui';
import { vehicleOrderManageApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';
import { message } from 'antd';
import { modifyLogTableColumns } from '../../utils/constant';

interface UpdateLogModalProps {
  orderId: number;
}

const UpdateLogModal: React.FC<UpdateLogModalProps> = ({ orderId }) => {
  const [loading, setLoading] = useState(false);
  const [tableData, setTableData] = useState<any>({
    list: [],
    total: 0,
    pages: 0,
    pageNum: 1,
    pageSize: 10,
  });

  useEffect(() => {
    if (!Number(orderId)) return;
    loadUpdateLog();
  }, [orderId]);

  // 加载修改记录
  const loadUpdateLog = async (pageNum: number = 1, pageSize: number = 10) => {
    setLoading(true);
    try {
      const res = await vehicleOrderManageApi.pageUpdateLog({
        orderId: Number(orderId),
        pageNum,
        pageSize,
      });
      if (res && res.code === HttpStatusCode.Success && res.data) {
        setTableData({
          list: res.data.list || [],
          total: res.data.total || 0,
          pages: res.data.pages || 0,
          pageNum: res.data.pageNum || 1,
          pageSize: res.data.pageSize || 10,
        });
      } else {
        message.error(res.message || '获取修改记录失败');
      }
    } catch (error) {
      console.error('加载修改记录失败:', error);
      message.error('加载修改记录失败');
    } finally {
      setLoading(false);
    }
  };
  const handlePageChange = (params: any) => {
    loadUpdateLog(params.pageNum, params.pageSize);
  };

  return (
    <div style={{ padding: '16px 0' }}>
      <CommonTable
        columns={modifyLogTableColumns}
        tableListData={{
          list: tableData.list,
          totalNumber: tableData.total,
          totalPage:
            tableData.pages || Math.ceil(tableData.total / tableData.pageSize),
        }}
        loading={loading}
        searchCondition={{
          pageNum: tableData.pageNum,
          pageSize: tableData.pageSize,
        }}
        onPageChange={handlePageChange}
        rowKey="id"
      />
    </div>
  );
};

export default UpdateLogModal;
