import { message } from 'antd';
import { HttpStatusCode } from './constant';
import { buildURL, buildFetchInit, parseResponseData } from './util';
/**
 * @param {RequestOptions} param 接收参数
 * @param {string} [param.path] 请求地址
 * @param {boolean} [autoTip=true] 是否自动提示
 * @return {Promise}
 */

export const doRequest = async (param: RequestOptions, autoTip = true) => {
  if (param.useMock) {
    return {
      code: HttpStatusCode.Success,
      data: param.mockData,
    };
  }
  const url = buildURL(param);
  const fetchInit = buildFetchInit(param);
  try {
    const res = await window.fetch(url, fetchInit);
    const respBody = await parseResponseData(
      res,
      res.headers.get('Content-Type'),
    );
    const httpCode: any = res.status;
    if (httpCode < 200 || httpCode >= 300) {
      if (httpCode < 200) {
        return {
          code: `${httpCode}`,
          message: '网络错误',
        };
      } else if (httpCode === 401) {
        if (location.pathname !== '/login') {
          location.replace('/login');
          message.error('登录过期，请重新登录', 2);
        } else {
          message.error('登录过期，请重新登录', 2);
        }
      } else if (httpCode === 403) {
        location.replace('/app/403');
      } else if (httpCode >= 300) {
        return {
          code: `${httpCode}`,
          message: '网络错误',
        };
      }
    } else {
      return {
        code: HttpStatusCode.Success,
        ...respBody,
      };
    }
  } catch (e) {
    console.log(e);
  }
};

const defaultRequestOption: RequestOptions = {
  method: 'POST',
  contentType: 'application/json',
  timeout: 15000,
};

function makeTimeoutPromise(timeout: number) {
  return new Promise((resolve: any, reject: any) => {
    setTimeout(() => {
      resolve({
        code: '3001',
        message: '请求超时',
        requestId: '',
        eventType: '',
        traceId: '',
      });
    }, timeout);
  });
}

/**
 *
 * @param {RequestOptions} opts 请求参数
 * @return {Promise}
 */
export async function request(opts: RequestOptions) {
  const options = Object.assign({}, defaultRequestOption, opts);
  return Promise.race([
    makeTimeoutPromise(options.timeout!),
    doRequest(options),
  ]);
}
