import React, { useState, useEffect, useRef } from 'react';
import Map from 'ol/Map.js';
import View from 'ol/View.js';
import TileLayer from 'ol/layer/Tile.js';
import TileWMS from 'ol/source/TileWMS.js';
import { defaults as defaultControls } from 'ol/control';
import './index.scss';
import { MapLayerEnum } from './utils/enum';
import MapOperate from './utils/mapOperate';
import {
  ENABLE_ENUM,
  getStatusColor,
  initTileLayer,
  OtherTileLayer,
} from './utils/constant';
import TaskSearchForm from './components/TaskSearchForm';
import TaskForm from './components/TaskForm';
import Legend from './components/Legend';
import { isEmpty, debounce } from 'lodash';
import DeployMapApi, { LatLng } from '@/fetch/business/deployMap';
import { HttpStatusCode, Method } from '@/fetch/core/constant';
import { message, Table } from 'antd';
import VectorLayer from 'ol/layer/Vector';
import VectorSource from 'ol/source/Vector';
import { LineString } from 'ol/geom';
import { Feature, Overlay } from 'ol';
import { Tile } from 'ol/layer';
import { Stroke } from 'ol/style';
import { Style } from 'ol/style';
import {
  LayerIdEnum,
  TASK_ROUTE_COLOR,
  ELEMENT_TYPE,
  SearchType,
  TaskStatusTextMap,
  TaskStatusEnum,
} from '@/utils/constant';
import CreateRouteBtn from './components/CreateRouteBtn';
import MapCenterSearch from './components/MapCenterSearch';
import StationStopMarkerManager from '@/utils/DeployMapTool/StationStopMarker';
import { DoubleClickZoom } from 'ol/interaction';
import { request } from '@/fetch/core';
import { showModal } from '@/components';
import XYZ from 'ol/source/XYZ';
import {
  transformLocationGcj02towgs84,
  transformLocationWgs84togcj02,
} from '@/utils/tencentMap';
// 搜索表单参数
interface SearchFormParams {
  taskName?: string | null;
  taskStatus?: string | null;
  stationId?: number | null;
  creatorUsername?: string | null;
}

// 完整的搜索参数
interface SearchParams {
  type: SearchType;
  latitude: number | string | null;
  longitude: number | string | null;
  elementType: ELEMENT_TYPE | null;
  elementId?: string | null;
}
const DeployMap = () => {
  const mapRef = useRef<any>(null); // 地图实例
  const mapContainerRef = useRef<any>(null); // 地图的dom元素
  const mapOperateRef = useRef<any>(null); // 地图操作方法的类的实例
  const StationStopMarkerRef = useRef<any>(null); // 地图上站点停靠点的操作类的实例
  const selectedTaskIdRef = useRef<number | null>(null);
  const [searchVal, setSearchVal] = useState<SearchFormParams>();
  const [mapCenterSearchInfo, setMapCenterSearchInfo] = useState<SearchParams>({
    type: SearchType.ELEMENT,
    latitude: null,
    longitude: null,
    elementType: null,
    elementId: null,
  });
  const [selectedTaskId, setSelectedTaskId] = useState<number | null>(null);
  const taskFormRef = useRef<any>(null);
  const [taskList, setTaskList] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const mapRoutesLayerRef = useRef<any>(null);
  const [isEditing, setIsEditing] = useState(false);
  const isEditingRef = useRef<boolean>(false);
  const [stationList, setStationList] = useState<any[]>([]);
  const [stopList, setStopList] = useState<any[]>([]);
  const [init, setInit] = useState<boolean>(true);
  const [startPoint, setStartPoint] = useState<any>(null);
  const [endPoint, setEndPoint] = useState<any>(null);
  const [selectedTaskInfo, setSelectedTaskInfo] = useState<any>(null);
  const taskDetailOverlayRef = useRef<any>(null);
  const showPhoneNumberRef = useRef<boolean>(false);
  const mapInfoRef = useRef({
    id: null,
    version: null,
  });
  const onMarkerSelect = () => {};
  useEffect(() => {
    if (!mapRef.current) {
      const mapLayer = new Tile({
        source: new XYZ({
          url: 'https://rt3.map.gtimg.com/realtimerender?z={z}&x={x}&y={-y}&type=vector&style=1',
          maxZoom: 18,
          interpolate: true, // 启用瓦片插值
        }),
      });
      mapRef.current = new Map({
        target: mapContainerRef.current,
        controls: defaultControls({ rotate: false }),
        layers: [mapLayer],
        view: new View({
          center: [105.5576, 30.7852],
          projection: 'EPSG:4326',
          zoom: 4,
          maxZoom: 22,
          minZoom: 5,
          constrainRotation: true,
          constrainResolution: false,
          rotation: 0,
          enableRotation: false,
        }),
      });
      mapRef.current.getInteractions().forEach((interaction) => {
        if (interaction instanceof DoubleClickZoom) {
          interaction.setActive(false);
        }
      });
      mapOperateRef.current = new MapOperate(mapRef.current);
      StationStopMarkerRef.current = new StationStopMarkerManager(
        mapRef.current,
        onMarkerSelect,
      );
    }
    mapRef.current.on('click', handleClickMapRoute);
    getStationList();
  }, []);

  useEffect(() => {
    if (!isEmpty(taskList)) {
      renderMapRoutes();
    } else {
      if (mapRoutesLayerRef.current) {
        mapRoutesLayerRef.current.getSource()?.clear();
      }
    }
  }, [taskList]);
  useEffect(() => {
    if (selectedTaskId) {
      const taskInfo = taskList.find((item) => item.taskId === selectedTaskId);
      if (taskInfo) {
        setSelectedTaskInfo(taskInfo);
      }
    } else {
      setSelectedTaskInfo(null);
    }
  }, [selectedTaskId]);

  useEffect(() => {
    if (!init && mapInfoRef.current.id && mapInfoRef.current.version) {
      // 保存高精地图图层的显隐状态
      const hdMapLayerVisibility = {};
      const hdMapLayers = [
        MapLayerEnum.CENTER_LINE,
        MapLayerEnum.GATE_LAYER,
        MapLayerEnum.STOP_LINE,
        MapLayerEnum.OPEN_AREA,
        MapLayerEnum.LINE_BOUNDARY,
        MapLayerEnum.INTERSECTION,
      ];

      // 获取并保存每个图层的显隐状态
      hdMapLayers.forEach((layerId) => {
        const layer = mapOperateRef.current.getLayer(layerId);
        if (layer) {
          hdMapLayerVisibility[layerId] = layer.getVisible();
        }
      });

      // mapid mapversion变化后需清空之前mapid mapversion对应的高精地图
      hdMapLayers.forEach((v) => mapOperateRef.current.clearLayer(v));

      // 重新创建图层并应用保存的显隐状态
      createInitTileLayer(hdMapLayerVisibility);
    }
  }, [init, mapInfoRef.current.id, mapInfoRef.current.version]);

  // 起点/终点变化时，更新地图上的标记信息
  useEffect(() => {
    if (mapOperateRef.current) {
      if (startPoint) {
        mapOperateRef.current.updateStartOrEndPoint('start', startPoint);
      } else {
        mapOperateRef.current.removeStartOrEndPoint('start');
      }
    }
  }, [startPoint]);

  useEffect(() => {
    if (mapOperateRef.current) {
      if (endPoint) {
        mapOperateRef.current.updateStartOrEndPoint('end', endPoint);
      } else {
        mapOperateRef.current.removeStartOrEndPoint('end');
      }
    }
  }, [endPoint]);

  // 监听编辑状态变化，确保线路图层的显隐状态正确
  useEffect(() => {
    if (mapOperateRef.current) {
      // 编辑状态下强制隐藏线路图层
      if (isEditing) {
        // 使用延迟确保即使图层尚未完全加载也能正确设置其可见性
        mapOperateRef.current.changeLayerVisible(
          LayerIdEnum.PLANNED_ROUTE_LAYER,
          false,
          0, // 初始重试次数
          10, // 增加最大重试次数
          200, // 增加重试延迟
        );
      }
    }
  }, [isEditing]);

  const getStationList = async () => {
    try {
      const res = await DeployMapApi.getStationList();
      if (res.code === HttpStatusCode.Success) {
        if (!isEmpty(res.data)) {
          setStationList(
            res.data.map((item: any) => ({
              pointId: item.id,
              pointName: item.name,
            })),
          );
        }
      }
    } catch (err) {
      console.error(err);
      message.error('系统异常，请稍后重试');
    }
  };

  const createTaskDetailOverlay = async (taskId: number, latlng: LatLng) => {
    try {
      const res = await DeployMapApi.getTaskDetail(taskId);
      if (res.code === HttpStatusCode.Success) {
        const taskDetail = res.data;
        const parsedPhoneNumberRes = await request({
          method: Method.POST,
          path: '/k2/management/common/get_user_phone',
          body: {
            userName: taskDetail.taskCreator,
          },
        });
        let parsedPhoneNumber = '';
        if (parsedPhoneNumberRes.code === HttpStatusCode.Success) {
          parsedPhoneNumber = parsedPhoneNumberRes.data.phone;
        }

        const updateOverlayContent = () => {
          popoverElement.innerHTML = `
            <div class="task-detail-overlay-title">${taskDetail.taskName}</div>
            <div class="task-status-badge" style="background-color: ${getStatusColor(
              taskDetail.taskStatus,
            )}">
              ${TaskStatusTextMap.get(taskDetail.taskStatus as TaskStatusEnum)}
            </div>
            <div class="task-detail-overlay-content">
              <div class="task-detail-overlay-item">
                <div class="task-detail-overlay-item-label">所属站点</div>
                <div class="task-detail-overlay-item-value">${
                  taskDetail.stationName
                }</div>
              </div>
              <div class='task-detail-overlay-item'>
                <div class="task-detail-overlay-item-label">所属城市</div>
                <div class="task-detail-overlay-item-value">${
                  taskDetail.cityName
                }</div>
              </div>
              <div class="task-detail-overlay-item">
                <div class="task-detail-overlay-item-label">负责人</div>
                <div class="task-detail-overlay-item-value">${
                  taskDetail.taskCreator
                }</div>
              </div>
              <div class="task-detail-overlay-item">
                <div class="task-detail-overlay-item-label">创建时间</div>
                <div class="task-detail-overlay-item-value">${
                  taskDetail.createTime
                }</div>
              </div>
              <div class="task-detail-overlay-item">
                <div class="task-detail-overlay-item-label">负责人电话</div>
                <div class="task-detail-overlay-item-value">${
                  !showPhoneNumberRef.current
                    ? taskDetail.phoneNumber
                    : parsedPhoneNumber
                }</div>
          ${
            !showPhoneNumberRef.current
              ? `<div class='show-phone-number' style='cursor:pointer'><svg class="icon" style="width: 1em;height: 1em;vertical-align: middle;fill: currentColor;overflow: hidden;" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="522"><path d="M512 298.666667c-162.133333 0-285.866667 68.266667-375.466667 213.333333 89.6 145.066667 213.333333 213.333333 375.466667 213.333333s285.866667-68.266667 375.466667-213.333333c-89.6-145.066667-213.333333-213.333333-375.466667-213.333333z m0 469.333333c-183.466667 0-328.533333-85.333333-426.666667-256 98.133333-170.666667 243.2-256 426.666667-256s328.533333 85.333333 426.666667 256c-98.133333 170.666667-243.2 256-426.666667 256z m0-170.666667c46.933333 0 85.333333-38.4 85.333333-85.333333s-38.4-85.333333-85.333333-85.333333-85.333333 38.4-85.333333 85.333333 38.4 85.333333 85.333333 85.333333z m0 42.666667c-72.533333 0-128-55.466667-128-128s55.466667-128 128-128 128 55.466667 128 128-55.466667 128-128 128z" fill="#444444" p-id="523"></path></svg></div>`
              : `<div class='hide-phone-number' style='cursor:pointer'><svg class="icon" style="width: 1em;height: 1em;vertical-align: middle;fill: currentColor;overflow: hidden;" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="537"><path d="M332.8 729.6l34.133333-34.133333c42.666667 12.8 93.866667 21.333333 145.066667 21.333333 162.133333 0 285.866667-68.266667 375.466667-213.333333-46.933333-72.533333-102.4-128-166.4-162.133334l29.866666-29.866666c72.533333 42.666667 132.266667 106.666667 183.466667 192-98.133333 170.666667-243.2 256-426.666667 256-59.733333 4.266667-119.466667-8.533333-174.933333-29.866667z m-115.2-64c-51.2-38.4-93.866667-93.866667-132.266667-157.866667 98.133333-170.666667 243.2-256 426.666667-256 38.4 0 76.8 4.266667 110.933333 12.8l-34.133333 34.133334c-25.6-4.266667-46.933333-4.266667-76.8-4.266667-162.133333 0-285.866667 68.266667-375.466667 213.333333 34.133333 51.2 72.533333 93.866667 115.2 128l-34.133333 29.866667z m230.4-46.933333l29.866667-29.866667c8.533333 4.266667 21.333333 4.266667 29.866666 4.266667 46.933333 0 85.333333-38.4 85.333334-85.333334 0-12.8 0-21.333333-4.266667-29.866666l29.866667-29.866667c12.8 17.066667 17.066667 38.4 17.066666 64 0 72.533333-55.466667 128-128 128-17.066667-4.266667-38.4-12.8-59.733333-21.333333zM384 499.2c4.266667-68.266667 55.466667-119.466667 123.733333-123.733333 0 4.266667-123.733333 123.733333-123.733333 123.733333zM733.866667 213.333333l29.866666 29.866667-512 512-34.133333-29.866667L733.866667 213.333333z" fill="#444444" p-id="538"></path></svg></div>`
          }
              </div>
              <div class="task-detail-overlay-item">
                <div class="task-detail-overlay-item-label">线路长度</div>
                <div class="task-detail-overlay-item-value">${
                  taskDetail.totalMileage
                }km</div>
              </div>
            ${
              taskDetail.taskStatus === TaskStatusEnum.TASK_CLOSED
                ? `<div class="task-detail-overlay-item">
                <div class="task-detail-overlay-item-label">子任务数</div>
                <div class="task-detail-overlay-item-value">${taskDetail.subTaskList?.length}</div>
                <div class="check-subTask-detail">查看详情</div>
              </div>`
                : ''
            }
            </div>
          `;

          // 重新绑定事件
          const showButton = popoverElement.querySelector('.show-phone-number');
          const hideButton = popoverElement.querySelector('.hide-phone-number');
          const checkBtn = popoverElement.querySelector(
            '.check-subTask-detail',
          );

          if (showButton) {
            showButton.addEventListener('click', () => {
              showPhoneNumberRef.current = true;
              updateOverlayContent();
            });
          }

          if (hideButton) {
            hideButton.addEventListener('click', () => {
              showPhoneNumberRef.current = false;
              updateOverlayContent();
            });
          }

          if (checkBtn) {
            checkBtn.addEventListener('click', () => {
              showModal({
                title: '任务详情',
                content: (
                  <div>
                    <Table
                      scroll={{ y: 400 }}
                      columns={[
                        {
                          title: '开始时间',
                          dataIndex: 'startTime',
                          key: 'startTime',
                          render: (text) => <> {text || '-'}</>,
                        },
                        {
                          title: '结束时间',
                          dataIndex: 'endTime',
                          key: 'endTime',
                          render: (text) => <> {text || '-'}</>,
                        },
                      ]}
                      dataSource={taskDetail.subTaskList}
                      pagination={false}
                    />
                  </div>
                ),
                footer: {
                  showCancel: true,
                  cancelText: '关闭',
                  cancelFunc: (cb) => {
                    cb();
                  },
                },
              });
            });
          }
        };

        const popoverElement = document.createElement('div');
        popoverElement.className = 'task-detail-overlay';

        // 初始化内容和事件
        updateOverlayContent();

        removeTaskDetailOverlay();

        taskDetailOverlayRef.current = new Overlay({
          element: popoverElement,
          position: [latlng.longitude, latlng.latitude],
          positioning: 'bottom-center',
          offset: [0, -50],
          autoPan: true,
          className: 'task-detail-overlay-container',
        });

        mapRef.current.addOverlay(taskDetailOverlayRef.current);
      }
    } catch (err) {
      console.error(err);
      message.error('系统异常，请稍后重试');
    }
  };
  const removeTaskDetailOverlay = () => {
    if (taskDetailOverlayRef.current) {
      mapRef.current.removeOverlay(taskDetailOverlayRef.current);
    }
  };
  const renderMapRoutes = () => {
    const features: any[] = [];
    for (let item of taskList) {
      if (!isEmpty(item.taskRouteList)) {
        const feature = new Feature({
          geometry: new LineString(
            item.taskRouteList.map((latlng: LatLng) => [
              latlng.longitude,
              latlng.latitude,
            ]),
          ),
        });
        feature.setStyle(
          new Style({
            stroke: new Stroke({
              color: TASK_ROUTE_COLOR[item.taskRouteColor], // 主线颜色
              width: 6, // 主线宽度
              lineCap: 'round',
              lineJoin: 'round',
            }),
          }),
        );
        feature.set('taskId', item.taskId);
        feature.set('color', TASK_ROUTE_COLOR[item.taskRouteColor]);
        features.push(feature);
      }
    }
    if (mapRoutesLayerRef.current) {
      mapRoutesLayerRef.current.getSource()?.clear();
      mapRoutesLayerRef.current.getSource().addFeatures(features);
    } else {
      const source = new VectorSource({
        features: features,
      });
      const layer = new VectorLayer({
        source: source,
        zIndex: 1001,
      });
      layer.set('layerId', LayerIdEnum.PLANNED_ROUTE_LAYER);
      mapRoutesLayerRef.current = layer;
      mapRef.current.addLayer(layer);
    }
  };

  // 防抖处理的任务列表获取
  const debouncedGetTaskList = debounce(
    async (params: SearchParams & SearchFormParams) => {
      try {
        setLoading(true);
        const res = await DeployMapApi.getTaskRouteList({
          elementId: params.elementId,
          latitude: params.latitude,
          longitude: params.longitude,
          elementType: params.elementType,
          taskName: params.taskName,
          taskStatus: params.taskStatus,
          stationId: params.stationId,
          creatorUsername: params.creatorUsername,
        });
        if (res.code === HttpStatusCode.Success) {
          if (!isEmpty(res.data.taskList)) {
            setTaskList(
              res.data.taskList.map((item: any) => {
                return {
                  ...item,
                  taskRouteList: item.taskRouteList.map((latlng: LatLng) => {
                    const gcj02Latlng = transformLocationWgs84togcj02({
                      lon: latlng.longitude,
                      lat: latlng.latitude,
                    });
                    return {
                      longitude: gcj02Latlng.lon,
                      latitude: gcj02Latlng.lat,
                    };
                  }),
                };
              }),
            );
          } else {
            setTaskList([]);
          }
          setSelectedTaskId(null);
          selectedTaskIdRef.current = null;
          mapInfoRef.current = {
            id: res.data.mapId,
            version: res.data.mapVersion,
          };
          removeTaskDetailOverlay();
          if (params.type === SearchType.LOCATION) {
            getInitialPoints({
              latitude: res.data.elementLat,
              longitude: res.data.elementLon,
            });
            const gcj02Latlng = transformLocationGcj02towgs84({
              lon: params.longitude,
              lat: params.latitude,
            });
            changeSearchPoint(gcj02Latlng.lat, gcj02Latlng.lon);
          } else {
            changeSearchPoint(res.data.elementLat, res.data.elementLon);
            if (!isEmpty(res.data.pointList)) {
              const stationPointList = res.data.pointList
                .filter((item: any) => item.pointType === ELEMENT_TYPE.STATION)
                ?.map((item: any) => {
                  const gcj02Latlng = transformLocationWgs84togcj02({
                    lon: item.longitude,
                    lat: item.latitude,
                  });
                  return {
                    ...item,
                    longitude: gcj02Latlng.lon,
                    latitude: gcj02Latlng.lat,
                  };
                });
              const stopPointList = res.data.pointList
                .filter((item: any) => item.pointType === ELEMENT_TYPE.STOP)
                ?.map((item: any) => {
                  const gcj02Latlng = transformLocationWgs84togcj02({
                    lon: item.longitude,
                    lat: item.latitude,
                  });
                  return {
                    ...item,
                    longitude: gcj02Latlng.lon,
                    latitude: gcj02Latlng.lat,
                  };
                });

              setStationList(stationPointList);
              setStopList(stopPointList);
              createMapStationStop(stationPointList, stopPointList);
            }
          }
        } else {
          message.error(res.msg || '获取任务列表失败');
        }
      } catch (err) {
        console.error(err);
        message.error('系统异常，请稍后重试');
      } finally {
        setLoading(false);
      }
    },
    300,
  );

  const renderSelectStartEnd = () => {
    return `<svg
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <circle cx="8" cy="8" r="8" fill="#12B35D" />
          <circle cx="8" cy="8" r="6" fill="white" />
          <circle cx="8" cy="8" r="4" fill="#12B35D" />
        </svg>
        <div class="start">设为起点</div>
        <svg
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <circle cx="8" cy="8" r="8" fill="#FC3737" />
          <circle cx="8" cy="8" r="6" fill="white" />
          <circle cx="8" cy="8" r="4" fill="#FC3737" />
        </svg>
        <div class="end">设为终点</div>`;
  };

  const createMapStationStop = (
    stationPointList: any[],
    stopPointList: any[],
  ) => {
    if (!StationStopMarkerRef.current) {
      return;
    }
    const COMMON_LABEL_STYLE = {
      fontSize: '16px',
      color: 'rgba(35,37,43,1)',
      fontFamily: 'PingFang SC',
      fontWeight: '500' as const,
    };

    const COMMON_POPOVER_STYLE = {
      backgroundColor: 'rgba(255,255,255,0.9)',
      boxShadow: '0 2px 12px 0 rgba(35,37,43,0.1)',
      height: '32px',
      maxWidth: '200px',
      borderRadius: '8px',
    };
    const popoverContent = renderSelectStartEnd();
    const handlePopoverClick = (type: string, point) => {
      if (!isEditingRef.current) {
        setIsEditing(true);
        isEditingRef.current = true;
        mapOperateRef.current?.changeLayerVisible(
          LayerIdEnum.PLANNED_ROUTE_LAYER,
          false,
        );
      }
      type === 'start'
        ? setStartPoint({
            pointName: point.pointName,
            latitude: point.latitude,
            longitude: point.longitude,
            pointId: point.pointId,
          })
        : setEndPoint({
            pointName: point.pointName,
            latitude: point.latitude,
            longitude: point.longitude,
            pointId: point.pointId,
          });
      StationStopMarkerRef.current.deselectAll();
    };
    const createMarkers = ({ icon, points }) =>
      points.map((point) => ({
        id: `${point.pointType}-${point.pointId}`,
        lngLat: [point.longitude, point.latitude],
        iconPath: require(`../../assets/image/mapCollect/${icon}-icon.png`),
        selectedIconPath: require(`../../assets/image/mapCollect/${icon}-icon.png`),
        map: mapRef.current,
        disabled: point.enabled === ENABLE_ENUM.DISABLE,
        pointType: point.pointType,
        scale: 0.2,
        label: point.pointName,
        labelStyle: COMMON_LABEL_STYLE,
        popoverContent,
        popoverStyle: COMMON_POPOVER_STYLE,
        onPopoverClick: (type) => handlePopoverClick(type, point),
      }));

    StationStopMarkerRef.current.addMarkers([
      ...createMarkers({ icon: 'station', points: stationPointList }),
      ...createMarkers({ icon: 'stop', points: stopPointList }),
    ]);
  };
  const changeSearchPoint = (latitude, longitude) => {
    const gcj02Latlng = transformLocationWgs84togcj02({
      lon: longitude,
      lat: latitude,
    });
    mapOperateRef.current.changeCenter(gcj02Latlng.lat, gcj02Latlng.lon);
    mapOperateRef.current.changeSearchPoint([gcj02Latlng.lon, gcj02Latlng.lat]);
  };

  const getInitialPoints = async (params: {
    latitude: number;
    longitude: number;
  }) => {
    try {
      const res = await DeployMapApi.getInitialPoints(params);
      if (res.code === HttpStatusCode.Success) {
        if (res.data) {
          if (!isEmpty(res.data.pointList)) {
            const stationPointList = res.data.pointList
              .filter((item: any) => item.pointType === ELEMENT_TYPE.STATION)
              ?.map((item: any) => {
                const gcj02Latlng = transformLocationWgs84togcj02({
                  lon: item.longitude,
                  lat: item.latitude,
                });
                return {
                  ...item,
                  longitude: gcj02Latlng.lon,
                  latitude: gcj02Latlng.lat,
                };
              });
            const stopPointList = res.data.pointList
              .filter((item: any) => item.pointType === ELEMENT_TYPE.STOP)
              ?.map((item: any) => {
                const gcj02Latlng = transformLocationWgs84togcj02({
                  lon: item.longitude,
                  lat: item.latitude,
                });
                return {
                  ...item,
                  longitude: gcj02Latlng.lon,
                  latitude: gcj02Latlng.lat,
                };
              });
            setStationList(stationPointList);
            setStopList(stopPointList);
            createMapStationStop(stationPointList, stopPointList);
          } else {
            setStationList([]);
            setStopList([]);
          }
        }
      }
    } catch (err) {
      console.error(err);
      message.error('系统异常，请稍后重试');
    }
  };

  const handleSearch = (values: SearchFormParams) => {
    const newSearchVal = {
      ...searchVal,
      latitude: mapCenterSearchInfo.latitude,
      longitude: mapCenterSearchInfo.longitude,
      ...values,
    };
    setSearchVal(newSearchVal);
    debouncedGetTaskList({
      ...mapCenterSearchInfo,
      ...newSearchVal,
    });
  };

  const handleClickMapRoute = (event: any): void => {
    const pixel = mapRef.current.getEventPixel(event.originalEvent);
    const clickCoordinate = mapRef.current.getCoordinateFromPixel(pixel);
    const features = mapRef.current.getFeaturesAtPixel(pixel, {
      layerFilter: (layer) => {
        return layer.get('layerId') === LayerIdEnum.PLANNED_ROUTE_LAYER;
      },
      hitTolerance: 5, // 增加点击容差，使用户更容易点击到线条
    });

    if (taskFormRef.current) {
      if (!isEmpty(features)) {
        // 如果有多个features，找到距离点击位置最近的那个
        if (features.length > 1) {
          let closestFeature = features[0];
          let minDistance = Infinity;

          features.forEach((feature) => {
            // 对于线要素，找到线上最近的点
            if (feature.getGeometry().getType() === 'LineString') {
              const closestPoint = feature
                .getGeometry()
                .getClosestPoint(clickCoordinate);
              const distance = Math.sqrt(
                Math.pow(closestPoint[0] - clickCoordinate[0], 2) +
                  Math.pow(closestPoint[1] - clickCoordinate[1], 2),
              );

              if (distance < minDistance) {
                minDistance = distance;
                closestFeature = feature;
              }
            }
          });

          taskFormRef.current.handleSelectTask(closestFeature.get('taskId'));
        } else {
          // 只有一个feature的情况
          taskFormRef.current.handleSelectTask(features[0].get('taskId'));
        }
      } else {
        if (selectedTaskIdRef.current) {
          taskFormRef.current.handleSelectTask(selectedTaskIdRef.current);
        }
        removeTaskDetailOverlay();
      }
    }
  };

  const getTaskList = async () => {
    debouncedGetTaskList({
      ...mapCenterSearchInfo,
      ...searchVal,
    });
  };

  const changeMapCenter = (val: SearchParams, type: 'type' | 'value') => {
    setMapCenterSearchInfo({ ...val });
    const initSearchVal = {
      taskName: null,
      taskStatus: null,
      stationId: null,
      creatorUsername: null,
    };
    setSearchVal(initSearchVal);
    init && type === 'value' && setInit(false);
    if (type === 'type') {
      return;
    }
    clearMapElements();
    debouncedGetTaskList({
      ...val,
      ...initSearchVal,
    });
  };

  const createTileLayer = (layerInfo: any) => {
    try {
      if (!layerInfo) {
        console.error('Layer info is undefined or null');
        return null;
      }

      const mapInfo = mapInfoRef.current;
      if (!mapInfo.id || !mapInfo.version) {
        console.error('Map info is incomplete', mapInfo);
        return null;
      }

      // 检查必要的属性
      if (!layerInfo.url) {
        console.error('Layer URL is missing', layerInfo);
        return null;
      }

      if (!layerInfo.params) {
        console.error('Layer params are missing', layerInfo);
        return null;
      }

      // const cqlFilter = `map_id=${mapInfo.id} and map_version=${mapInfo.version}`;
      const cqlFilter = `map_id=${mapInfo.id}`;

      return new TileLayer({
        source: new TileWMS({
          url: layerInfo.url,
          params: {
            ...layerInfo.params,
            CQL_FILTER: cqlFilter,
          },
          transition: 300,
          cacheSize: 2048,
        }),
        preload: Infinity,
        useInterimTilesOnError: false,
      });
    } catch (error) {
      console.error('Error creating tile layer:', error, layerInfo);
      return null;
    }
  };

  const clearMapElements = () => {
    StationStopMarkerRef.current.clearAllMarkers();
  };

  const createInitTileLayer = (layerVisibility?: Record<string, boolean>) => {
    if (mapRef.current && mapOperateRef.current) {
      for (let item of initTileLayer) {
        try {
          const layer = createTileLayer(item);

          if (!layer) {
            console.error(`Failed to create layer for ${item.layerId}`);
            continue; // 跳过这个图层，继续处理下一个
          }

          mapOperateRef.current.addLayers([
            {
              layer: layer,
              layerId: item.layerId,
            },
          ]);

          // 应用保存的图层显隐状态
          if (layerVisibility && layerVisibility[item.layerId] !== undefined) {
            // 使用延迟确保图层已完全加载
            setTimeout(() => {
              mapOperateRef.current.changeLayerVisible(
                item.layerId,
                layerVisibility[item.layerId],
                0, // 初始重试次数
                10, // 最大重试次数
                100, // 重试延迟
              );
            }, 200);
          }
        } catch (error) {
          console.error(`Error processing layer ${item.layerId}:`, error);
          // 继续处理下一个图层
        }
      }
    }
  };

  const addOtherTileLayer = async (layerId: MapLayerEnum): Promise<boolean> => {
    try {
      const mapOperate = mapOperateRef.current;
      if (!mapOperate) {
        throw new Error('Map instance is not available');
      }
      if (mapOperate.getLayer(layerId)) {
        return true;
      }
      const layerInfo = OtherTileLayer.get(layerId);
      if (!layerInfo) {
        console.error(`Layer info not found for ${layerId}`);
        return false;
      }

      const newLayer = createTileLayer(layerInfo);
      if (!newLayer) {
        console.error(`Failed to create layer for ${layerId}`);
        return false;
      }

      mapOperate.addLayers([
        {
          layer: newLayer,
          layerId: layerInfo.layerId,
        },
      ]);

      return true;
    } catch (error) {
      console.error(`Failed to add ${layerId} layer:`, error);
      return false; // 返回false而不是抛出错误，避免中断执行
    }
  };

  return (
    <div className="deploy-map">
      <div className="map-container" ref={mapContainerRef}>
        <MapCenterSearch
          mapCenterSearchInfo={mapCenterSearchInfo}
          changeMapCenter={(val, type) => changeMapCenter(val, type)}
        />
        {!init && (
          <>
            <Legend
              changeLayerVisible={mapOperateRef.current?.changeLayerVisible}
              stationStopMarkerRef={StationStopMarkerRef.current}
              isEditing={isEditing}
              checkLayerExist={(layerId: any) => {
                return addOtherTileLayer(layerId);
              }}
            />
            <CreateRouteBtn
              onClick={() => {
                if (!isEditing) {
                  setIsEditing(true);
                  isEditingRef.current = true;

                  // 使用延迟重试机制确保线路图层可见性正确设置
                  mapOperateRef.current?.changeLayerVisible(
                    LayerIdEnum.PLANNED_ROUTE_LAYER,
                    false,
                    0, // 初始重试次数
                    10, // 增加最大重试次数
                    200, // 增加重试延迟
                  );

                  if (selectedTaskId) {
                    setSelectedTaskId(null);
                    selectedTaskIdRef.current = null;
                    setSelectedTaskInfo(null);
                    removeTaskDetailOverlay();
                  }
                }
              }}
              isEditing={isEditing}
            />
          </>
        )}
      </div>

      {!isEditing ? (
        <TaskSearchForm
          map={mapRef.current}
          selectedTaskId={selectedTaskId}
          setSelectedTaskId={setSelectedTaskId}
          selectedTaskIdRef={selectedTaskIdRef}
          taskFormRef={taskFormRef}
          taskList={taskList}
          loading={loading}
          setLoading={setLoading}
          onSearch={handleSearch}
          stationList={stationList}
          setIsEditing={setIsEditing}
          isEditingRef={isEditingRef}
          isInit={!init}
          getTaskList={getTaskList}
          removeTaskDetailOverlay={removeTaskDetailOverlay}
          createTaskDetailOverlay={createTaskDetailOverlay}
        />
      ) : (
        <TaskForm
          stationList={stationList}
          taskId={selectedTaskId}
          setIsEditing={setIsEditing}
          map={mapRef.current}
          startPoint={startPoint}
          endPoint={endPoint}
          setStartPoint={setStartPoint}
          setEndPoint={setEndPoint}
          selectedTaskInfo={selectedTaskInfo}
          isEditingRef={isEditingRef}
          getTaskList={getTaskList}
          setSelectedTaskId={setSelectedTaskId}
          stopList={stopList}
        />
      )}
    </div>
  );
};

export default DeployMap;
