import React, { useEffect, useState } from 'react';
import { Form, Input, Select, InputNumber, message } from 'antd';
import cloneDeep from 'lodash/cloneDeep';
import { CommonEdit } from '@/components';
import { formatLocation, formatOptions } from '@/utils/utils';
import { ResourceManageApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';
import { PageType } from '@/utils/enum';
import { useCommonDropDown } from '@/utils/hooks';
import { dropDownListKey, dropDownKey } from '@/utils/constant';

const ResourceManageEdit = () => {
  const [formRef] = Form.useForm();
  const fetchApi = new ResourceManageApi();
  const { id, type, app } = formatLocation(window.location.search);
  let orderBy: any = null;
  if (type === PageType.ADD && !app) {
    orderBy = 1;
  }
  const [breadCrumbItems, setBreadCrumbItems] = useState([
    { title: '资源管理', route: '' },
    { title: '新建资源', route: '' },
  ]);
  const methodArr = [
    { label: 'GET', value: 'GET' },
    { label: 'POST', value: 'POST' },
    { label: 'PUT', value: 'PUT' },
    { label: 'DELETE', value: 'DELETE' },
  ];
  const [detailValues, setDetailValues] = useState<any>({
    enable: 1,
  });
  // clone一份详情,用于比较是否修改,未修改时点击确定不调用接口
  const [originDetailValues, setoriginDetailValues] = useState<any>();

  const dropdownList = useCommonDropDown([
    dropDownKey.RESOURCE_APP,
    dropDownKey.RESOURCE_TYPE,
  ]);
  useEffect(() => {
    if (type !== PageType.ADD && id) {
      setBreadCrumbItems([
        { title: '资源管理', route: '' },
        { title: '编辑资源', route: '' },
      ]);
      fetchApplicantDetail();
    } else {
      formRef.setFieldsValue({ ...detailValues });
    }
  }, []);

  const fetchApplicantDetail = async () => {
    const response: any = await fetchApi.fetchDetail(id);
    if (response.code === HttpStatusCode.Success) {
      const detail = response.data;
      formRef.setFieldsValue({ ...detail });
      setDetailValues(detail);
      setoriginDetailValues(cloneDeep(detail));
    } else {
      message.error(response.message);
    }
  };

  const confirmClick = () => {
    formRef.validateFields().then(async (formValue) => {
      if (type === PageType.EDIT && id && mapCompar(formValue)) {
        window.history.back();
        return;
      }
      const reqParams = { ...formValue };
      if (id && type === PageType.ADD) {
        reqParams.parentNumber = id;
      }
      if (id && type === PageType.EDIT) {
        reqParams.number = id;
      }
      const response = await fetchApi.submitEditInfo({
        type,
        requestBody: reqParams,
      });
      if (response.code === HttpStatusCode.Success) {
        message.success(response.message);
        window.history.back();
      } else {
        message.error(response.message);
      }
    });
  };

  const mapCompar = (formValue: any) => {
    // JSON.stringify 的第二个可选参数,过滤一些不必须要的属性
    const originDataStr = JSON.stringify(originDetailValues, (k, v) => {
      // 注意：第一次 k 是 undefined，v 是原对象
      if (!k || Object.prototype.hasOwnProperty.call(formValue, k)) {
        return v;
      }
    });
    const newDataStr = JSON.stringify(formValue);
    return originDataStr === newDataStr;
  };
  return (
    <CommonEdit
      title={type === PageType.EDIT && id ? '编辑资源' : '新建资源'}
      breadCrumbConfig={breadCrumbItems}
      onSubmitClick={confirmClick}
      onCancleClick={() => {
        window.history.back();
      }}
    >
      <Form
        form={formRef}
        initialValues={{ app: app, orderBy: orderBy }}
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 18 }}
        autoComplete="off"
      >
        {type === PageType.ADD && id && (
          <Form.Item label="父级资源编号">{id}</Form.Item>
        )}
        {type === PageType.ADD && (
          <Form.Item
            name="number"
            label="资源编号"
            rules={[{ required: true, message: '请输入资源编号' }]}
          >
            <Input placeholder="请输入资源编号	" />
          </Form.Item>
        )}
        {type === PageType.EDIT && detailValues.parentNumber && (
          <Form.Item label="父级资源编号">
            {detailValues.parentNumber}
          </Form.Item>
        )}
        {type === PageType.EDIT && id && (
          <Form.Item label="资源编号">{id}</Form.Item>
        )}
        <Form.Item
          name="name"
          label="资源名称	"
          rules={[{ required: true, message: '请输入资源名称' }]}
        >
          <Input placeholder="请输入资源名称	" />
        </Form.Item>
        <Form.Item
          name="description"
          label="资源描述"
          rules={[{ required: true, message: '请输入资源描述' }]}
        >
          <Input placeholder="请输入资源描述" />
        </Form.Item>
        <Form.Item
          name="app"
          label="应用系统"
          rules={[{ required: true, message: '请选择应用系统' }]}
        >
          <Select
            placeholder="请选择应用系统"
            options={formatOptions(dropdownList[dropDownListKey.RESOURCE_APP])}
            disabled={app ? true : false}
          />
        </Form.Item>
        <Form.Item
          name="type"
          label="资源类型"
          rules={[{ required: true, message: '请选择资源类型' }]}
        >
          <Select
            placeholder="请选择资源类型	"
            options={formatOptions(dropdownList[dropDownListKey.RESOURCE_TYPE])}
          />
        </Form.Item>
        <Form.Item
          name="resourceCode"
          label="资源标识"
          rules={[{ required: true, message: '请输入资源标识' }]}
        >
          <Input placeholder="请输入资源标识" />
        </Form.Item>
        <Form.Item
          name="path"
          label="路径"
          rules={[{ required: true, message: '请输入路径' }]}
        >
          <Input placeholder="请输入路径" />
        </Form.Item>
        <Form.Item
          name="method"
          label="方法"
          rules={[{ required: true, message: '请选择方法' }]}
        >
          <Select placeholder="请选择方法" options={methodArr} />
        </Form.Item>
        <Form.Item
          name="orderBy"
          label="排序值"
          rules={[{ required: true, message: '请输入排序值' }]}
        >
          <InputNumber
            min={1}
            step={1}
            placeholder="请输入排序值"
            precision={0}
            style={{ width: '100%' }}
          />
        </Form.Item>
      </Form>
    </CommonEdit>
  );
};

export default React.memo(ResourceManageEdit);
