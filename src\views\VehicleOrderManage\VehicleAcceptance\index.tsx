import React, { useState, useRef } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Card, Button, message } from 'antd';
import { UnorderedListOutlined } from '@ant-design/icons';
import { CommonForm } from '@jd/x-coreui';
import { BreadCrumb } from '@/components';
import { vehicleOrderManageApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';
import { vehicleAcceptanceFormConfig } from '../utils/constant';
import './index.scss';

const VehicleAcceptance: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [submitting, setSubmitting] = useState(false);
  const formRef = useRef<any>(null);

  const orderId = searchParams.get('id');

  const breadcrumbItems = [
    {
      title: '订单管理',
      route: '/app/vehicleOrderManage',
    },
    {
      title: '车辆验收单',
      route: '',
    },
  ];

  const defaultValues = {
    vehicleAppearanceStatus: true,
    vehiclePowerStatus: true,
    chargerEquippedStatus: true,
    spareKeyEquippedStatus: true,
  };

  const handleReset = () => {
    if (formRef.current) {
      const currentValues = formRef.current.getFieldsValue();
      const checkUser = currentValues.checkUser;
      formRef.current.resetFields();
      formRef.current.setFieldsValue({
        ...defaultValues,
        checkUser,
      });
    }
    message.success('已重置表单');
  };

  const handleSubmit = async () => {
    if (!Number(orderId)) {
      message.error('订单信息异常');
      return;
    }
    if (!formRef.current) {
      message.error('表单异常');
      return;
    }
    try {
      const values = await formRef.current.validateFields();
      setSubmitting(true);
      const submitData = {
        id: Number(orderId),
        ...(values || {}),
        imageList: values?.imageList?.map((item: any) => ({
          type: 'images',
          fileKey: item,
          bucketName: 'rover-operation',
        })),
        checkRemark: values?.checkRemark || '',
      };
      const response = await vehicleOrderManageApi.checkVehicle(submitData);
      if (response.code === HttpStatusCode.Success) {
        message.success('验收成功');
        navigate('/app/vehicleOrderManage');
      } else {
        message.error(response.message || '验收失败');
      }
    } catch (error: any) {
      if (error.errorFields) {
        message.error('请检查表单填写是否正确');
      } else {
        console.error('验收失败:', error);
        message.error('验收失败');
      }
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="vehicle-acceptance">
      <div style={{ padding: '15px 0' }}>
        <BreadCrumb items={breadcrumbItems} />
      </div>
      <Card className="acceptance-card">
        <div className="card-header">
          <UnorderedListOutlined className="header-icon" />
          <span className="header-title">车辆验收单</span>
        </div>
        <div className="card-content">
          <CommonForm
            formConfig={vehicleAcceptanceFormConfig}
            layout="horizontal"
            formType="edit"
            defaultValue={defaultValues}
            getFormInstance={(ref) => {
              formRef.current = ref;
            }}
          />
        </div>
      </Card>
      <div className="bottom-actions">
        <Button onClick={handleReset}>重置</Button>
        <Button type="primary" loading={submitting} onClick={handleSubmit}>
          提交
        </Button>
      </div>
    </div>
  );
};

export default React.memo(VehicleAcceptance);
